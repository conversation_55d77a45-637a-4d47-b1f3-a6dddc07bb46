{"__meta": {"id": "01JZ7GKZFF3EX9R22E6Q9AEA2S", "datetime": "2025-07-03 07:47:08", "utime": **********.01643, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.4.4", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.381702, "end": **********.01646, "duration": 0.6347579956054688, "duration_str": "635ms", "measures": [{"label": "Booting", "start": **********.381702, "relative_start": 0, "end": **********.627491, "relative_end": **********.627491, "duration": 0.****************, "duration_str": "246ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.627499, "relative_start": 0.*****************, "end": **********.016462, "relative_end": 2.1457672119140625e-06, "duration": 0.***************, "duration_str": "389ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.636208, "relative_start": 0.*****************, "end": **********.638004, "relative_end": **********.638004, "duration": 0.0017960071563720703, "duration_str": "1.8ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.013894, "relative_start": 0.****************, "end": **********.014653, "relative_end": **********.014653, "duration": 0.0007588863372802734, "duration_str": "759μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "40MB"}, "exceptions": {"count": 0, "exceptions": []}, "route": {"uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>"}, "queries": {"count": 86, "nb_statements": 85, "nb_visible_statements": 86, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.049, "accumulated_duration_str": "49ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, {"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Support/Utils.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Support\\Utils.php", "line": 114}, {"index": 17, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasPanelShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasPanelShield.php", "line": 17}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 208}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 54}], "start": **********.654234, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "FilamentShield.php:121", "source": {"index": 15, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FFilamentShield.php&line=121", "ajax": false, "filename": "FilamentShield.php", "line": "121"}, "connection": "mcabplpnet", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from \"roles\" where (\"name\" = 'panel_user') limit 1", "type": "query", "params": [], "bindings": ["panel_user"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, {"index": 21, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Support/Utils.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Support\\Utils.php", "line": 114}, {"index": 22, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasPanelShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasPanelShield.php", "line": 17}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 208}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 54}], "start": **********.654633, "duration": 0.022949999999999998, "duration_str": "22.95ms", "memory": 0, "memory_str": null, "filename": "FilamentShield.php:121", "source": {"index": 20, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FFilamentShield.php&line=121", "ajax": false, "filename": "FilamentShield.php", "line": "121"}, "connection": "mcabplpnet", "explain": null, "start_percent": 0, "width_percent": 46.837}, {"sql": "select * from \"users\" where \"id\" = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.6783571, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "mcabplpnet", "explain": null, "start_percent": 46.837, "width_percent": 1.388}, {"sql": "select * from \"libraries\" where \"status\" = 'Open' and \"libraries\".\"deleted_at\" is null", "type": "query", "params": [], "bindings": ["Open"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 18}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.9065108, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:18", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=18", "ajax": false, "filename": "ApiHealthStatus.php", "line": "18"}, "connection": "mcabplpnet", "explain": null, "start_percent": 48.224, "width_percent": 1.898}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 1 group by \"endpoint\"", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.9090219, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 50.122, "width_percent": 1.939}, {"sql": "select * from \"api_health_checks\" where \"id\" in (2, 3, 1)", "type": "query", "params": [], "bindings": [2, 3, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.9107442, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 52.061, "width_percent": 0.571}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 2 group by \"endpoint\"", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.911953, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 52.633, "width_percent": 0.592}, {"sql": "select * from \"api_health_checks\" where \"id\" in (5, 6, 4)", "type": "query", "params": [], "bindings": [5, 6, 4], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.912993, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 53.224, "width_percent": 0.449}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 3 group by \"endpoint\"", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.914021, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 53.673, "width_percent": 0.551}, {"sql": "select * from \"api_health_checks\" where \"id\" in (8, 9, 7)", "type": "query", "params": [], "bindings": [8, 9, 7], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.915045, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 54.224, "width_percent": 0.469}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 4 group by \"endpoint\"", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.9160812, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 54.694, "width_percent": 0.551}, {"sql": "select * from \"api_health_checks\" where \"id\" in (11, 12, 10)", "type": "query", "params": [], "bindings": [11, 12, 10], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.9170878, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 55.245, "width_percent": 0.49}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 5 group by \"endpoint\"", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.9181452, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 55.735, "width_percent": 0.571}, {"sql": "select * from \"api_health_checks\" where \"id\" in (14, 15, 13)", "type": "query", "params": [], "bindings": [14, 15, 13], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.919144, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 56.306, "width_percent": 0.429}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 6 group by \"endpoint\"", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.920204, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 56.735, "width_percent": 0.49}, {"sql": "select * from \"api_health_checks\" where \"id\" in (17, 18, 16)", "type": "query", "params": [], "bindings": [17, 18, 16], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.921933, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 57.224, "width_percent": 0.531}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 7 group by \"endpoint\"", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.92306, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 57.755, "width_percent": 0.633}, {"sql": "select * from \"api_health_checks\" where \"id\" in (20, 21, 19)", "type": "query", "params": [], "bindings": [20, 21, 19], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.924116, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 58.388, "width_percent": 0.51}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 8 group by \"endpoint\"", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.925235, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 58.898, "width_percent": 0.592}, {"sql": "select * from \"api_health_checks\" where \"id\" in (23, 24, 22)", "type": "query", "params": [], "bindings": [23, 24, 22], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.926285, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 59.49, "width_percent": 0.469}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 9 group by \"endpoint\"", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.927355, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 59.959, "width_percent": 0.612}, {"sql": "select * from \"api_health_checks\" where \"id\" in (26, 27, 25)", "type": "query", "params": [], "bindings": [26, 27, 25], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.9283872, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 60.571, "width_percent": 0.551}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 10 group by \"endpoint\"", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.929512, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 61.122, "width_percent": 0.673}, {"sql": "select * from \"api_health_checks\" where \"id\" in (29, 30, 28)", "type": "query", "params": [], "bindings": [29, 30, 28], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.930919, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 61.796, "width_percent": 0.51}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 11 group by \"endpoint\"", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.932027, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 62.306, "width_percent": 0.571}, {"sql": "select * from \"api_health_checks\" where \"id\" in (32, 33, 31)", "type": "query", "params": [], "bindings": [32, 33, 31], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.933029, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 62.878, "width_percent": 0.51}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 12 group by \"endpoint\"", "type": "query", "params": [], "bindings": [12], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.934133, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 63.388, "width_percent": 0.551}, {"sql": "select * from \"api_health_checks\" where \"id\" in (35, 36, 34)", "type": "query", "params": [], "bindings": [35, 36, 34], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.935144, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 63.939, "width_percent": 0.388}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 13 group by \"endpoint\"", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.936153, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 64.327, "width_percent": 0.51}, {"sql": "select * from \"api_health_checks\" where \"id\" in (38, 39, 37)", "type": "query", "params": [], "bindings": [38, 39, 37], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.9372442, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 64.837, "width_percent": 0.612}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 14 group by \"endpoint\"", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.938448, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 65.449, "width_percent": 0.776}, {"sql": "select * from \"api_health_checks\" where \"id\" in (41, 42, 40)", "type": "query", "params": [], "bindings": [41, 42, 40], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.940717, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 66.224, "width_percent": 1.184}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 15 group by \"endpoint\"", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.9427, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 67.408, "width_percent": 0.755}, {"sql": "select * from \"api_health_checks\" where \"id\" in (44, 45, 43)", "type": "query", "params": [], "bindings": [44, 45, 43], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.944512, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 68.163, "width_percent": 0.857}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 17 group by \"endpoint\"", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.946396, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 69.02, "width_percent": 0.551}, {"sql": "select * from \"api_health_checks\" where \"id\" in (47, 48, 46)", "type": "query", "params": [], "bindings": [47, 48, 46], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.947535, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 69.571, "width_percent": 0.673}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 18 group by \"endpoint\"", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.948979, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 70.245, "width_percent": 0.796}, {"sql": "select * from \"api_health_checks\" where \"id\" in (50, 49)", "type": "query", "params": [], "bindings": [50, 49], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.9502668, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 71.041, "width_percent": 0.878}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 19 group by \"endpoint\"", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.9516208, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 71.918, "width_percent": 0.857}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.952868, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 72.776, "width_percent": 0.816}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 20 group by \"endpoint\"", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.9544811, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 73.592, "width_percent": 0.898}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.956568, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 74.49, "width_percent": 0.694}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 21 group by \"endpoint\"", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.957796, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 75.184, "width_percent": 0.673}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.9595451, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 75.857, "width_percent": 0.551}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 23 group by \"endpoint\"", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.960547, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 76.408, "width_percent": 0.735}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.961595, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 77.143, "width_percent": 0.367}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 24 group by \"endpoint\"", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.9625409, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 77.51, "width_percent": 0.796}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.963798, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 78.306, "width_percent": 0.694}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 25 group by \"endpoint\"", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.965083, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 79, "width_percent": 0.796}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.966194, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 79.796, "width_percent": 0.51}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 26 group by \"endpoint\"", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.967227, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 80.306, "width_percent": 0.633}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.968316, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 80.939, "width_percent": 0.551}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 27 group by \"endpoint\"", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.969419, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 81.49, "width_percent": 0.592}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.970542, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 82.082, "width_percent": 0.857}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 28 group by \"endpoint\"", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.971824, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 82.939, "width_percent": 0.714}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.972986, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 83.653, "width_percent": 0.776}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 29 group by \"endpoint\"", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.974214, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 84.429, "width_percent": 0.694}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.975351, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 85.122, "width_percent": 0.551}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 30 group by \"endpoint\"", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.976457, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 85.673, "width_percent": 0.571}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.9774952, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 86.245, "width_percent": 0.551}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 31 group by \"endpoint\"", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.978531, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 86.796, "width_percent": 0.49}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.979455, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 87.286, "width_percent": 0.347}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 32 group by \"endpoint\"", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.9803612, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 87.633, "width_percent": 0.551}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.9813309, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 88.184, "width_percent": 0.388}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 33 group by \"endpoint\"", "type": "query", "params": [], "bindings": [33], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.982376, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 88.571, "width_percent": 0.714}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.984288, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 89.286, "width_percent": 0.714}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 34 group by \"endpoint\"", "type": "query", "params": [], "bindings": [34], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.9863489, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 90, "width_percent": 0.837}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.9875631, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 90.837, "width_percent": 0.653}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 35 group by \"endpoint\"", "type": "query", "params": [], "bindings": [35], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.988657, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 91.49, "width_percent": 0.571}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.989663, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 92.061, "width_percent": 0.469}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 36 group by \"endpoint\"", "type": "query", "params": [], "bindings": [36], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.9906359, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 92.531, "width_percent": 0.531}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.991633, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 93.061, "width_percent": 0.449}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 37 group by \"endpoint\"", "type": "query", "params": [], "bindings": [37], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.992626, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 93.51, "width_percent": 0.612}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.993641, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 94.122, "width_percent": 0.367}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 38 group by \"endpoint\"", "type": "query", "params": [], "bindings": [38], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.994748, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 94.49, "width_percent": 0.571}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.995735, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 95.061, "width_percent": 0.347}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 39 group by \"endpoint\"", "type": "query", "params": [], "bindings": [39], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.996655, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 95.408, "width_percent": 0.714}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.997721, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 96.122, "width_percent": 0.408}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 40 group by \"endpoint\"", "type": "query", "params": [], "bindings": [40], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.998671, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 96.531, "width_percent": 0.367}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.999512, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 96.898, "width_percent": 0.265}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 43 group by \"endpoint\"", "type": "query", "params": [], "bindings": [43], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.0003562, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 97.163, "width_percent": 0.571}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.001344, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 97.735, "width_percent": 0.327}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 44 group by \"endpoint\"", "type": "query", "params": [], "bindings": [44], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.0023751, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 98.061, "width_percent": 0.592}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.0033689, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 98.653, "width_percent": 0.469}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 45 group by \"endpoint\"", "type": "query", "params": [], "bindings": [45], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.0044148, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 99.122, "width_percent": 0.531}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.005395, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 99.653, "width_percent": 0.347}]}, "models": {"data": {"App\\Models\\ApiHealthCheck": {"value": 100, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FModels%2FApiHealthCheck.php&line=1", "ajax": false, "filename": "ApiHealthCheck.php", "line": "?"}}, "App\\Models\\Library": {"value": 41, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FModels%2FLibrary.php&line=1", "ajax": false, "filename": "Library.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 143, "is_counter": true}, "livewire": {"data": {"app.filament.widgets.api-health-status #wTEScz94DBK5c266Oqbm": "array:4 [\n  \"data\" => []\n  \"name\" => \"app.filament.widgets.api-health-status\"\n  \"component\" => \"App\\Filament\\Widgets\\ApiHealthStatus\"\n  \"id\" => \"wTEScz94DBK5c266Oqbm\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG", "PHPDEBUGBAR_STACK_DATA": "[]", "_previous": "array:1 [\n  \"url\" => \"http://bplpnet.test/admin\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "23", "password_hash_web": "$2y$10$ndp0mY0vfh7SKC/wbKghcOK8n.7TdyC6/Z5j84PViILPym72FiJWy", "48040ef7f2542b39b9ba9a72983b0d88_filters": "array:3 [\n  \"library\" => null\n  \"startDate\" => null\n  \"endDate\" => null\n]"}, "request": {"data": {"status": "200 OK", "full_url": "http://bplpnet.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "telescope": "<a href=\"http://bplpnet.test/_debugbar/telescope/9f4cdfe7-387a-4ee2-b897-a753de97ca43\" target=\"_blank\">View in Telescope</a>", "duration": "643ms", "peak_memory": "42MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1214241556 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1214241556\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1653282044 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"308 characters\">{&quot;data&quot;:[],&quot;memo&quot;:{&quot;id&quot;:&quot;wTEScz94DBK5c266Oqbm&quot;,&quot;name&quot;:&quot;app.filament.widgets.api-health-status&quot;,&quot;path&quot;:&quot;admin&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;lazyLoaded&quot;:false,&quot;lazyIsolated&quot;:true,&quot;errors&quot;:[],&quot;locale&quot;:&quot;ar&quot;},&quot;checksum&quot;:&quot;464b766c39c1086f5d21e56b8254861614593784db099a6df2f306ea12a25389&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"10 characters\">__lazyLoad</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"344 characters\">eyJkYXRhIjp7ImZvck1vdW50IjpbeyJmaWx0ZXJzIjpbeyJsaWJyYXJ5IjpudWxsLCJzdGFydERhdGUiOm51bGwsImVuZERhdGUiOm51bGx9LHsicyI6ImFyciJ9XX0seyJzIjoiYXJyIn1dfSwibWVtbyI6eyJpZCI6IkdmZE1hRTBHbFkwc0FXUktTZUxEIiwibmFtZSI6Il9fbW91bnRQYXJhbXNDb250YWluZXIifSwiY2hlY2tzdW0iOiJkY2VjZmYwN2Q2ODJiMTgyMDc0OTE0MjQ1Y2QxOGY5ZTUzZTI5YWQ5NDliOTJhNDc4MGRlOGNmYTc1Zjk2ODc2In0=</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1653282044\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1443607730 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1555 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjhlNFJweU9qTWYyaXc4SEtWMDl3OGc9PSIsInZhbHVlIjoibnJRVzFVMC9nV09rS2xUUjNERDZJVG9TVlM3M3Q1RXU0S3hVMW9QRVRpMlQrc25qSm9UbFIwV1kzSzNqTUtaVU1xNExkV09NZHFLSjIyR1M3NTB1enJzbDI2bmRabmFON0VYSlN5OTFaMUdDUVVUSmMyYW15RmtjSmhtUmgyZVkzclRrTXd6eWpKbG5DUzFPY0JmaE1KQzRDVjUzQkp4Ukl4aUwwOXlRZXN6dllPL2dPbWllZE42QTRKbGFuczNMNVFENmlsaXVZb3lmcHNkZkRMejJqZDQzSHd3V3N2cU13MDJKbFJoZ0RkST0iLCJtYWMiOiI2NTRlYzEyZTY5ODY1MzRlMmY4YmM5MTM4MTdhN2Q3YWQwYzQzZTZiOGQ3YmFmZDg3YWFjMzJhMTgyYzBiMTc3IiwidGFnIjoiIn0%3D; filament_language_switch_locale=eyJpdiI6IldSalJ4dW1CcFhqWCtSR2pFSytLYUE9PSIsInZhbHVlIjoiQTVEaTRtaXhMVlhxNTBXZEUrOSt3UDVoQUJvOGdCa21ZODBwekJ5aDc5bjJ2SzgySVR1YnpMOVNWWlpSdjVWcyIsIm1hYyI6ImUyNjI0NmUyMzk3MjI2MmUyNzdiY2JhOGVhMzE0YjRjOWIyNzJiODUxMGU1NzQzYTJhY2M1YjIwYjc0ZTJkOTgiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6InBod2JWb1FWT0FTQmp2MGE0SldiK1E9PSIsInZhbHVlIjoiMnNLWTU5S2FJMUhzSmxWbjQ2eTNkcnJLd0dGbmwzeG9IdE9aUEl6Yk5JdFAyWHMvQ3BwUDdiSHhKWXNnQ0tCMTlIc1FoRU5vbjMvWUg5dHlENjd0aXNOS3g4dkJ3L016cVBkYVBFcERIQkFGZG1xZ3pmRmw2ZDhORnRXaDFBbUMiLCJtYWMiOiIyNTdlNTI5ZTg4YjAxYmYyOTBhYzc0ZTI2N2NkMzk2ZWQzZjMyZDA1MzExMmYyZTdkNzEzMDk2ZTI4NzJkZDFmIiwidGFnIjoiIn0%3D; alfhrs_almohd_session=eyJpdiI6IjNqaGdQWGJpc3dTODh0Ylg4SnozdFE9PSIsInZhbHVlIjoiRjlJZ2JaOVRna2Fab21vRVVBQkxXOUp2Tk00NEdEaTBISHVyanBLa2VycTZHSEhweHVxVEpYaWUxV1pJSDdERHYvYTdCYUN3c1J2dmp0dmNTK0RTZ25vTzJTcGxrSmw1RnRGL0dRK25kNUJERUFQVlFFcmcrZWlIcGNwSzE0THAiLCJtYWMiOiJlZTIzMTdmOTQyZTU1OTU0NDY5OGY5M2Y0NzYxNTVlZTlhNGVhMTEyMTFkMjk0MjIxNjBjOTFhZWZiYzZjMjlhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">ar-EG,ar;q=0.9,en-US;q=0.8,en;q=0.7,fr;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://bplpnet.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">http://bplpnet.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">847</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">bplpnet.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1443607730\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1538657714 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"124 characters\">23|oayZmX4hCkVRzDYgX8XiyDlbdUWGhjg8WRylkmFAigbARBfuImkVfTcPvqxq|$2y$10$ndp0mY0vfh7SKC/wbKghcOK8n.7TdyC6/Z5j84PViILPym72FiJWy</span>\"\n  \"<span class=sf-dump-key>filament_language_switch_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG</span>\"\n  \"<span class=sf-dump-key>alfhrs_almohd_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OHrIdfK5jqy65dnTOAgZp53RllI0Bol0hjZv4x8G</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1538657714\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1584388255 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 03 Jul 2025 06:47:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1584388255\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-788020956 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://bplpnet.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$10$ndp0mY0vfh7SKC/wbKghcOK8n.7TdyC6/Z5j84PViILPym72FiJWy</span>\"\n  \"<span class=sf-dump-key>48040ef7f2542b39b9ba9a72983b0d88_filters</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>library</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>startDate</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>endDate</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-788020956\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://bplpnet.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}