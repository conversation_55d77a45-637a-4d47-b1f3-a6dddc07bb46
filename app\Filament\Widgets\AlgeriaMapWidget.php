<?php

namespace App\Filament\Widgets;

use App\Filament\Resources\LibraryResource;
use App\Models\Library;
use App\Enums\LibraryStatus;
use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Log; // Keep Log facade if needed for debugging elsewhere

class AlgeriaMapWidget extends Widget
{
    protected static string $view = 'filament.widgets.algeria-map-widget';

    // Make it span full width in most Filament layouts
    protected int | string | array $columnSpan = 'full';

    protected static ?int $sort = 2;
}

