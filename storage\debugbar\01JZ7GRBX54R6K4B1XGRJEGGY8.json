{"__meta": {"id": "01JZ7GRBX54R6K4B1XGRJEGGY8", "datetime": "2025-07-03 07:49:31", "utime": **********.813696, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.4.4", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.281771, "end": **********.813737, "duration": 0.531965970993042, "duration_str": "532ms", "measures": [{"label": "Booting", "start": **********.281771, "relative_start": 0, "end": **********.562631, "relative_end": **********.562631, "duration": 0.*****************, "duration_str": "281ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.562644, "relative_start": 0.*****************, "end": **********.813739, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "251ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.574151, "relative_start": 0.*****************, "end": **********.576658, "relative_end": **********.576658, "duration": 0.0025069713592529297, "duration_str": "2.51ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.810619, "relative_start": 0.****************, "end": **********.811538, "relative_end": **********.811538, "duration": 0.0009188652038574219, "duration_str": "919μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "40MB"}, "exceptions": {"count": 0, "exceptions": []}, "route": {"uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>"}, "queries": {"count": 86, "nb_statements": 85, "nb_visible_statements": 86, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.07716000000000003, "accumulated_duration_str": "77.16ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, {"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Support/Utils.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Support\\Utils.php", "line": 114}, {"index": 17, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasPanelShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasPanelShield.php", "line": 17}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 208}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 54}], "start": **********.599105, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "FilamentShield.php:121", "source": {"index": 15, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FFilamentShield.php&line=121", "ajax": false, "filename": "FilamentShield.php", "line": "121"}, "connection": "mcabplpnet", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from \"roles\" where (\"name\" = 'panel_user') limit 1", "type": "query", "params": [], "bindings": ["panel_user"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, {"index": 21, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Support/Utils.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Support\\Utils.php", "line": 114}, {"index": 22, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasPanelShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasPanelShield.php", "line": 17}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 208}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 54}], "start": **********.599717, "duration": 0.03284, "duration_str": "32.84ms", "memory": 0, "memory_str": null, "filename": "FilamentShield.php:121", "source": {"index": 20, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FFilamentShield.php&line=121", "ajax": false, "filename": "FilamentShield.php", "line": "121"}, "connection": "mcabplpnet", "explain": null, "start_percent": 0, "width_percent": 42.561}, {"sql": "select * from \"users\" where \"id\" = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.634089, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "mcabplpnet", "explain": null, "start_percent": 42.561, "width_percent": 1.413}, {"sql": "select * from \"libraries\" where \"status\" = 'Open' and \"libraries\".\"deleted_at\" is null", "type": "query", "params": [], "bindings": ["Open"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 19}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6474319, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:19", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=19", "ajax": false, "filename": "ApiHealthStatus.php", "line": "19"}, "connection": "mcabplpnet", "explain": null, "start_percent": 43.974, "width_percent": 1.426}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 1 group by \"endpoint\"", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.650665, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 45.399, "width_percent": 1.983}, {"sql": "select * from \"api_health_checks\" where \"id\" in (2, 3, 1)", "type": "query", "params": [], "bindings": [2, 3, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6535919, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 47.382, "width_percent": 0.765}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 2 group by \"endpoint\"", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.656403, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 48.147, "width_percent": 0.648}, {"sql": "select * from \"api_health_checks\" where \"id\" in (5, 6, 4)", "type": "query", "params": [], "bindings": [5, 6, 4], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6577759, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 48.795, "width_percent": 0.467}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 3 group by \"endpoint\"", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.659126, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 49.261, "width_percent": 0.622}, {"sql": "select * from \"api_health_checks\" where \"id\" in (8, 9, 7)", "type": "query", "params": [], "bindings": [8, 9, 7], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.660449, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 49.883, "width_percent": 0.428}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 4 group by \"endpoint\"", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.661788, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 50.311, "width_percent": 0.596}, {"sql": "select * from \"api_health_checks\" where \"id\" in (11, 12, 10)", "type": "query", "params": [], "bindings": [11, 12, 10], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6632328, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 50.907, "width_percent": 0.557}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 5 group by \"endpoint\"", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.664831, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 51.464, "width_percent": 0.648}, {"sql": "select * from \"api_health_checks\" where \"id\" in (14, 15, 13)", "type": "query", "params": [], "bindings": [14, 15, 13], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.666613, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 52.112, "width_percent": 0.454}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 6 group by \"endpoint\"", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.667931, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 52.566, "width_percent": 0.428}, {"sql": "select * from \"api_health_checks\" where \"id\" in (17, 18, 16)", "type": "query", "params": [], "bindings": [17, 18, 16], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6691222, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 52.994, "width_percent": 0.57}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 7 group by \"endpoint\"", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.671594, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 53.564, "width_percent": 0.7}, {"sql": "select * from \"api_health_checks\" where \"id\" in (20, 21, 19)", "type": "query", "params": [], "bindings": [20, 21, 19], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6742232, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 54.264, "width_percent": 0.596}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 8 group by \"endpoint\"", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.675783, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 54.86, "width_percent": 0.648}, {"sql": "select * from \"api_health_checks\" where \"id\" in (23, 24, 22)", "type": "query", "params": [], "bindings": [23, 24, 22], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.677246, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 55.508, "width_percent": 0.596}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 9 group by \"endpoint\"", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6794581, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 56.104, "width_percent": 0.804}, {"sql": "select * from \"api_health_checks\" where \"id\" in (26, 27, 25)", "type": "query", "params": [], "bindings": [26, 27, 25], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.68167, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 56.908, "width_percent": 0.674}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 10 group by \"endpoint\"", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.683332, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 57.582, "width_percent": 0.739}, {"sql": "select * from \"api_health_checks\" where \"id\" in (29, 30, 28)", "type": "query", "params": [], "bindings": [29, 30, 28], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.684884, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 58.32, "width_percent": 0.661}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 11 group by \"endpoint\"", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.687573, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 58.981, "width_percent": 0.739}, {"sql": "select * from \"api_health_checks\" where \"id\" in (32, 33, 31)", "type": "query", "params": [], "bindings": [32, 33, 31], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.689023, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 59.72, "width_percent": 0.441}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 12 group by \"endpoint\"", "type": "query", "params": [], "bindings": [12], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.690273, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 60.161, "width_percent": 0.48}, {"sql": "select * from \"api_health_checks\" where \"id\" in (35, 36, 34)", "type": "query", "params": [], "bindings": [35, 36, 34], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.691466, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 60.64, "width_percent": 0.402}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 13 group by \"endpoint\"", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6927552, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 61.042, "width_percent": 0.57}, {"sql": "select * from \"api_health_checks\" where \"id\" in (38, 39, 37)", "type": "query", "params": [], "bindings": [38, 39, 37], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.694076, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 61.612, "width_percent": 0.454}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 14 group by \"endpoint\"", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6960351, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 62.066, "width_percent": 0.622}, {"sql": "select * from \"api_health_checks\" where \"id\" in (41, 42, 40)", "type": "query", "params": [], "bindings": [41, 42, 40], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.697474, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 62.688, "width_percent": 0.609}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 15 group by \"endpoint\"", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.699505, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 63.297, "width_percent": 0.674}, {"sql": "select * from \"api_health_checks\" where \"id\" in (44, 45, 43)", "type": "query", "params": [], "bindings": [44, 45, 43], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.702012, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 63.971, "width_percent": 0.674}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 17 group by \"endpoint\"", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.703635, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 64.645, "width_percent": 0.635}, {"sql": "select * from \"api_health_checks\" where \"id\" in (47, 48, 46)", "type": "query", "params": [], "bindings": [47, 48, 46], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.705873, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 65.28, "width_percent": 0.726}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 18 group by \"endpoint\"", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.7078679, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 66.006, "width_percent": 0.739}, {"sql": "select * from \"api_health_checks\" where \"id\" in (50, 49)", "type": "query", "params": [], "bindings": [50, 49], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.709952, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 66.744, "width_percent": 0.661}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 19 group by \"endpoint\"", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.711889, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 67.405, "width_percent": 0.933}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.714208, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 68.339, "width_percent": 0.778}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 20 group by \"endpoint\"", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.716857, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 69.116, "width_percent": 0.855}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.7188108, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 69.971, "width_percent": 0.778}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 21 group by \"endpoint\"", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.720934, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 70.749, "width_percent": 0.791}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.723149, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 71.54, "width_percent": 0.713}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 23 group by \"endpoint\"", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.725169, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 72.252, "width_percent": 0.778}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.727963, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 73.03, "width_percent": 0.842}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 24 group by \"endpoint\"", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.730262, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 73.872, "width_percent": 0.933}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.73249, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 74.806, "width_percent": 0.726}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 25 group by \"endpoint\"", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.735034, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 75.531, "width_percent": 1.024}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.7380521, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 76.555, "width_percent": 0.829}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 26 group by \"endpoint\"", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.740502, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 77.385, "width_percent": 0.804}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.742821, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 78.188, "width_percent": 0.622}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 27 group by \"endpoint\"", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.74442, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 78.81, "width_percent": 0.687}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.746027, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 79.497, "width_percent": 0.596}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 28 group by \"endpoint\"", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.748435, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 80.093, "width_percent": 0.907}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.7504559, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 81.001, "width_percent": 0.713}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 29 group by \"endpoint\"", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.752336, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 81.713, "width_percent": 0.816}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.754924, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 82.53, "width_percent": 0.687}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 30 group by \"endpoint\"", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.7569919, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 83.217, "width_percent": 0.765}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.759648, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 83.981, "width_percent": 0.907}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 31 group by \"endpoint\"", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.7615328, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 84.889, "width_percent": 0.778}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.763438, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 85.666, "width_percent": 0.583}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 32 group by \"endpoint\"", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.7653341, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 86.249, "width_percent": 0.648}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.767681, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 86.897, "width_percent": 0.557}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 33 group by \"endpoint\"", "type": "query", "params": [], "bindings": [33], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.769771, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 87.455, "width_percent": 0.609}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.771295, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 88.064, "width_percent": 0.389}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 34 group by \"endpoint\"", "type": "query", "params": [], "bindings": [34], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.772505, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 88.453, "width_percent": 0.635}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.7739968, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 89.088, "width_percent": 0.557}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 35 group by \"endpoint\"", "type": "query", "params": [], "bindings": [35], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.775348, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 89.645, "width_percent": 0.531}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.776721, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 90.176, "width_percent": 0.544}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 36 group by \"endpoint\"", "type": "query", "params": [], "bindings": [36], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.7780418, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 90.721, "width_percent": 0.531}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.7794108, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 91.252, "width_percent": 0.557}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 37 group by \"endpoint\"", "type": "query", "params": [], "bindings": [37], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.781203, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 91.809, "width_percent": 0.661}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.782588, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 92.47, "width_percent": 0.363}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 38 group by \"endpoint\"", "type": "query", "params": [], "bindings": [38], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.783655, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 92.833, "width_percent": 0.324}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.7847872, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 93.157, "width_percent": 0.363}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 39 group by \"endpoint\"", "type": "query", "params": [], "bindings": [39], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.785903, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 93.52, "width_percent": 0.48}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.7870588, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 93.999, "width_percent": 0.544}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 40 group by \"endpoint\"", "type": "query", "params": [], "bindings": [40], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.788471, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 94.544, "width_percent": 0.544}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.7898932, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 95.088, "width_percent": 0.609}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 43 group by \"endpoint\"", "type": "query", "params": [], "bindings": [43], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.791691, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 95.697, "width_percent": 0.881}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.794554, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 96.579, "width_percent": 0.829}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 44 group by \"endpoint\"", "type": "query", "params": [], "bindings": [44], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.79637, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 97.408, "width_percent": 0.752}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.797963, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 98.16, "width_percent": 0.518}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 45 group by \"endpoint\"", "type": "query", "params": [], "bindings": [45], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.799358, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 98.678, "width_percent": 0.726}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.8010252, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 99.404, "width_percent": 0.596}]}, "models": {"data": {"App\\Models\\ApiHealthCheck": {"value": 100, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FModels%2FApiHealthCheck.php&line=1", "ajax": false, "filename": "ApiHealthCheck.php", "line": "?"}}, "App\\Models\\Library": {"value": 41, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FModels%2FLibrary.php&line=1", "ajax": false, "filename": "Library.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 143, "is_counter": true}, "livewire": {"data": {"app.filament.widgets.api-health-status #KoUDFWwQV3z1A3yuMOI6": "array:4 [\n  \"data\" => []\n  \"name\" => \"app.filament.widgets.api-health-status\"\n  \"component\" => \"App\\Filament\\Widgets\\ApiHealthStatus\"\n  \"id\" => \"KoUDFWwQV3z1A3yuMOI6\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG", "PHPDEBUGBAR_STACK_DATA": "[]", "_previous": "array:1 [\n  \"url\" => \"http://bplpnet.test/admin\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "23", "password_hash_web": "$2y$10$ndp0mY0vfh7SKC/wbKghcOK8n.7TdyC6/Z5j84PViILPym72FiJWy", "48040ef7f2542b39b9ba9a72983b0d88_filters": "array:3 [\n  \"library\" => null\n  \"startDate\" => null\n  \"endDate\" => null\n]"}, "request": {"data": {"status": "200 OK", "full_url": "http://bplpnet.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "telescope": "<a href=\"http://bplpnet.test/_debugbar/telescope/9f4ce0c2-a473-4a28-a73f-1f1a890c9633\" target=\"_blank\">View in Telescope</a>", "duration": "542ms", "peak_memory": "42MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-545682888 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-545682888\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-631308096 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"308 characters\">{&quot;data&quot;:[],&quot;memo&quot;:{&quot;id&quot;:&quot;KoUDFWwQV3z1A3yuMOI6&quot;,&quot;name&quot;:&quot;app.filament.widgets.api-health-status&quot;,&quot;path&quot;:&quot;admin&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;lazyLoaded&quot;:false,&quot;lazyIsolated&quot;:true,&quot;errors&quot;:[],&quot;locale&quot;:&quot;ar&quot;},&quot;checksum&quot;:&quot;75283380c913f6a6b928775f4c6b6f30a87685eaf1ffb187ac48c8ab0cebccc8&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"10 characters\">__lazyLoad</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"344 characters\">eyJkYXRhIjp7ImZvck1vdW50IjpbeyJmaWx0ZXJzIjpbeyJsaWJyYXJ5IjpudWxsLCJzdGFydERhdGUiOm51bGwsImVuZERhdGUiOm51bGx9LHsicyI6ImFyciJ9XX0seyJzIjoiYXJyIn1dfSwibWVtbyI6eyJpZCI6IjhTTk5PQkUzQWVWM1QxcTZ4cGszIiwibmFtZSI6Il9fbW91bnRQYXJhbXNDb250YWluZXIifSwiY2hlY2tzdW0iOiJhNDcwMDJiMTY4NzQ5ZjBhZjE2NGY3MGY5NGJmNDc0YTAwYjM4ODc2OWIyYmY5MzA4MWRhYjdmZWE4YjJjM2MzIn0=</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-631308096\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1673272952 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1555 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjhlNFJweU9qTWYyaXc4SEtWMDl3OGc9PSIsInZhbHVlIjoibnJRVzFVMC9nV09rS2xUUjNERDZJVG9TVlM3M3Q1RXU0S3hVMW9QRVRpMlQrc25qSm9UbFIwV1kzSzNqTUtaVU1xNExkV09NZHFLSjIyR1M3NTB1enJzbDI2bmRabmFON0VYSlN5OTFaMUdDUVVUSmMyYW15RmtjSmhtUmgyZVkzclRrTXd6eWpKbG5DUzFPY0JmaE1KQzRDVjUzQkp4Ukl4aUwwOXlRZXN6dllPL2dPbWllZE42QTRKbGFuczNMNVFENmlsaXVZb3lmcHNkZkRMejJqZDQzSHd3V3N2cU13MDJKbFJoZ0RkST0iLCJtYWMiOiI2NTRlYzEyZTY5ODY1MzRlMmY4YmM5MTM4MTdhN2Q3YWQwYzQzZTZiOGQ3YmFmZDg3YWFjMzJhMTgyYzBiMTc3IiwidGFnIjoiIn0%3D; filament_language_switch_locale=eyJpdiI6IldSalJ4dW1CcFhqWCtSR2pFSytLYUE9PSIsInZhbHVlIjoiQTVEaTRtaXhMVlhxNTBXZEUrOSt3UDVoQUJvOGdCa21ZODBwekJ5aDc5bjJ2SzgySVR1YnpMOVNWWlpSdjVWcyIsIm1hYyI6ImUyNjI0NmUyMzk3MjI2MmUyNzdiY2JhOGVhMzE0YjRjOWIyNzJiODUxMGU1NzQzYTJhY2M1YjIwYjc0ZTJkOTgiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6IjdWYmxQV2U2N0R6VUduaVNVcGpEMXc9PSIsInZhbHVlIjoiSEhPdDRKNFFyQSt6NlI4NFpWQzQvYXBiRkdtSVhzOE56bGNvZFJaUUhYb3ZJYXRvVnpoVnpsblNHcGpwcUp3Ykd1eStibWxra0VlckVQMUZlZnIvWVZ4SHk3S2xqYUVsUHZXYjBRdEpLbGRERzN0dVFYU3J0dTVFd1NDK0tuQ04iLCJtYWMiOiI0YmU4NTM4ZTcyMWY4ZmFjZWY0MjhjYmY4N2M2ZDBhZDk5MGQyOWExZThjNjUxNjkwMjVjODlmOWQxMjU1ZjcwIiwidGFnIjoiIn0%3D; alfhrs_almohd_session=eyJpdiI6Iko5cVJrNnBualNFVCtWTVNIZk8rdkE9PSIsInZhbHVlIjoicTFtdmpyQUhVVGJ6ekZvd1l1eHJ2V0RIbG1Kc3FOaHkwUlBSRlEwRSswUVlrNndBQm9NQkhHS2pLQnI2L0VHMloxelR5Y05oaUVOT1dmSzU4QUNySUlzWVRBUm9rUWsyc1pjSmZFWXVkbDBIZHRZaHNoMFY4dXJ4TDNmcDBueFYiLCJtYWMiOiI4NTdlOTdhY2IzZDQ1ZGNmNTNkMGUwNDJhOTdhMmVlYWZhYWRiOGY0ODVlYjYzMWMzYTBjZjA1OGJlZWEyNjNjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">ar-EG,ar;q=0.9,en-US;q=0.8,en;q=0.7,fr;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://bplpnet.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">http://bplpnet.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">847</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">bplpnet.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1673272952\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-364941521 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"124 characters\">23|oayZmX4hCkVRzDYgX8XiyDlbdUWGhjg8WRylkmFAigbARBfuImkVfTcPvqxq|$2y$10$ndp0mY0vfh7SKC/wbKghcOK8n.7TdyC6/Z5j84PViILPym72FiJWy</span>\"\n  \"<span class=sf-dump-key>filament_language_switch_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG</span>\"\n  \"<span class=sf-dump-key>alfhrs_almohd_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OHrIdfK5jqy65dnTOAgZp53RllI0Bol0hjZv4x8G</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-364941521\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-570294048 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 03 Jul 2025 06:49:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-570294048\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-260279708 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://bplpnet.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$10$ndp0mY0vfh7SKC/wbKghcOK8n.7TdyC6/Z5j84PViILPym72FiJWy</span>\"\n  \"<span class=sf-dump-key>48040ef7f2542b39b9ba9a72983b0d88_filters</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>library</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>startDate</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>endDate</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-260279708\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://bplpnet.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}