<?php

namespace App\Filament\Widgets;

use App\Models\DocumentRecord;
use App\Models\Library;
use App\Models\LibraryStats;
use Carbon\Carbon;
use Filament\Widgets\Concerns\InteractsWithPageFilters;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

class StatsOverview extends BaseWidget
{
    use InteractsWithPageFilters;

    protected static ?int $sort = -1;

    protected function getStats(): array
    {
        $startDate = filled($this->filters['startDate'] ?? null) ?
            Carbon::parse($this->filters['startDate']) :
            null;

        $endDate = filled($this->filters['endDate'] ?? null) ?
            Carbon::parse($this->filters['endDate']) :
            now();
        $library = $this->filters['library'] ?? null;

        // 1. Optimized DocumentRecord stats
        $documentRecordQuery = DocumentRecord::query();
        if ($library) {
            $documentRecordQuery->where('library_id', $library);
        }
        $documentCounts = $documentRecordQuery
            ->selectRaw("COUNT(*) as total_count, SUM(CASE WHEN document_type = 'كتاب' THEN 1 ELSE 0 END) as book_count")
            ->first();

        $totalNotices = $documentCounts->total_count ?? 0;
        $totalBookNotices = $documentCounts->book_count ?? 0;

        // 2. Optimized Library stats
        $libraryStatusCounts = Library::query()
            ->selectRaw("SUM(CASE WHEN status = 'Open' THEN 1 ELSE 0 END) as open_libraries_count, SUM(CASE WHEN status = 'Closed' THEN 1 ELSE 0 END) as closed_libraries_count")
            ->first();

        $openLibrariesCount = $libraryStatusCounts->open_libraries_count ?? 0;
        $closedLibrariesCount = $libraryStatusCounts->closed_libraries_count ?? 0;

        // 3. Optimized LibraryStats
        $libraryStatsQuery = LibraryStats::query();
        if ($library) {
            $libraryStatsQuery->where('library_id', $library);
        }
        $statsAggregates = $libraryStatsQuery
            ->selectRaw("SUM(CAST(COALESCE(NULLIF(total_readers, ''), '0') AS INTEGER)) as sum_total_readers, SUM(CAST(COALESCE(NULLIF(total_readers_female, ''), '0') AS INTEGER)) as sum_total_readers_female, SUM(CAST(COALESCE(NULLIF(total_copies, ''), '0') AS INTEGER)) as sum_total_copies")
            ->first();

        $sumTotalReaders = $statsAggregates->sum_total_readers ?? 0;
        $sumTotalReadersFemale = $statsAggregates->sum_total_readers_female ?? 0;
        $sumTotalCopies = $statsAggregates->sum_total_copies ?? 0;

        return [
            // Total Notices
            Stat::make(__('global.stats.total_notice'), number_format($totalNotices, 0, ',', ','))
                ->label(__('global.stats.total_notice'))
                ->description(__('global.stats.total_notice_type_books') . ': ' . number_format($totalBookNotices, 0, ',', ','))
                ->descriptionIcon('heroicon-m-document-text')
                ->chart([7, 12, 18, 15, 22, 19, 25])
                ->color('success')
                ->extraAttributes([
                    'class' => 'relative overflow-hidden bg-gradient-to-br from-[#4a7c59] via-[#6b9b73] to-[#e6b366] dark:from-[#1d3a1e] dark:via-[#2d5a30] dark:to-[#bf822b] text-white shadow-lg hover:shadow-xl shadow-[#4a7c59]/20 hover:shadow-[#e6b366]/30 dark:shadow-[#1d3a1e]/30 hover:dark:shadow-[#bf822b]/40 transition-all duration-300 transform hover:-translate-y-1 hover:scale-105 [&_.fi-stat-label]:text-lg [&_.fi-stat-label]:font-bold [&_.fi-stat-label]:text-yellow-100 [&_.fi-stat-label]:dark:text-yellow-200 [&_.fi-stat-label]:tracking-wide',
                    'style' => '--stat-description-color: rgba(255, 255, 255, 0.9); --stat-value-color: #ffffff;',
                ]),

            // Total Libraries
            Stat::make(__('global.stats.total_libraries'), number_format($openLibrariesCount, 0, ',', ','))
                ->description(__('global.stats.total_libraries_connected') . ': ' . number_format($closedLibrariesCount, 0, ',', ','))
                ->descriptionIcon('heroicon-m-building-library')
                ->chart([5, 8, 12, 16, 20, 18, 24])
                ->color('success')
                ->extraAttributes([
                    'class' => 'relative overflow-hidden bg-gradient-to-br from-[#4a7c59] via-[#6b9b73] to-[#e6b366] dark:from-[#1d3a1e] dark:via-[#2d5a30] dark:to-[#bf822b] text-white shadow-lg hover:shadow-xl shadow-[#4a7c59]/20 hover:shadow-[#e6b366]/30 dark:shadow-[#1d3a1e]/30 hover:dark:shadow-[#bf822b]/40 transition-all duration-300 transform hover:-translate-y-1 hover:scale-105 [&_.fi-stat-label]:text-lg [&_.fi-stat-label]:font-bold [&_.fi-stat-label]:text-yellow-100 [&_.fi-stat-label]:dark:text-yellow-200 [&_.fi-stat-label]:tracking-wide',
                    'style' => '--stat-description-color: rgba(255, 255, 255, 0.9); --stat-value-color: #ffffff;',
                ]),

            // Total Readers
            Stat::make(__('global.stats.total_readers'), number_format($sumTotalReaders, 0, ',', ','))
                ->description(__('global.stats.total_readers_female') . ': ' . number_format($sumTotalReadersFemale, 0, ',', ','))
                ->descriptionIcon('heroicon-m-users')
                ->chart([10, 15, 20, 18, 25, 22, 30])
                ->color('success')
                ->extraAttributes([
                    'class' => 'relative overflow-hidden bg-gradient-to-br from-[#4a7c59] via-[#6b9b73] to-[#e6b366] dark:from-[#1d3a1e] dark:via-[#2d5a30] dark:to-[#bf822b] text-white shadow-lg hover:shadow-xl shadow-[#4a7c59]/20 hover:shadow-[#e6b366]/30 dark:shadow-[#1d3a1e]/30 hover:dark:shadow-[#bf822b]/40 transition-all duration-300 transform hover:-translate-y-1 hover:scale-105 cursor-pointer [&_.fi-stat-label]:text-lg [&_.fi-stat-label]:font-bold [&_.fi-stat-label]:text-yellow-100 [&_.fi-stat-label]:dark:text-yellow-200 [&_.fi-stat-label]:tracking-wide',
                    'style' => '--stat-description-color: rgba(255, 255, 255, 0.9); --stat-value-color: #ffffff;',
                    // 'wire:click' => "\$dispatch('setStatusFilter', { filter: 'processed' })",
                ]),

            // Total Copies
            Stat::make(__('cruds.librarystats.fields.total_copies'), number_format($sumTotalCopies, 0, ',', ','))
                ->descriptionIcon('heroicon-m-book-open')
                ->chart([8, 14, 19, 16, 23, 20, 28])
                ->color('success')
                ->extraAttributes([
                    'class' => 'relative overflow-hidden bg-gradient-to-br from-[#4a7c59] via-[#6b9b73] to-[#e6b366] dark:from-[#1d3a1e] dark:via-[#2d5a30] dark:to-[#bf822b] text-white shadow-lg hover:shadow-xl shadow-[#4a7c59]/20 hover:shadow-[#e6b366]/30 dark:shadow-[#1d3a1e]/30 hover:dark:shadow-[#bf822b]/40 transition-all duration-300 transform hover:-translate-y-1 hover:scale-105 [&_.fi-stat-label]:text-lg [&_.fi-stat-label]:font-bold [&_.fi-stat-label]:text-yellow-100 [&_.fi-stat-label]:dark:text-yellow-200 [&_.fi-stat-label]:tracking-wide',
                    'style' => '--stat-description-color: rgba(255, 255, 255, 0.9); --stat-value-color: #ffffff;',
                ]),
        ];
    }
}
