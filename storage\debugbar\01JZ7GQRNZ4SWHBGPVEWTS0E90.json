{"__meta": {"id": "01JZ7GQRNZ4SWHBGPVEWTS0E90", "datetime": "2025-07-03 07:49:12", "utime": **********.128661, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.4.4", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.721749, "end": **********.128701, "duration": 0.406951904296875, "duration_str": "407ms", "measures": [{"label": "Booting", "start": **********.721749, "relative_start": 0, "end": **********.998355, "relative_end": **********.998355, "duration": 0.*****************, "duration_str": "277ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.998369, "relative_start": 0.*****************, "end": **********.128704, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "130ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.009282, "relative_start": 0.****************, "end": **********.011366, "relative_end": **********.011366, "duration": 0.0020837783813476562, "duration_str": "2.08ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.125656, "relative_start": 0.*****************, "end": **********.126604, "relative_end": **********.126604, "duration": 0.0009481906890869141, "duration_str": "948μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "route": {"uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>"}, "queries": {"count": 5, "nb_statements": 4, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03384, "accumulated_duration_str": "33.84ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, {"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Support/Utils.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Support\\Utils.php", "line": 114}, {"index": 17, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasPanelShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasPanelShield.php", "line": 17}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 208}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 54}], "start": **********.032268, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "FilamentShield.php:121", "source": {"index": 15, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FFilamentShield.php&line=121", "ajax": false, "filename": "FilamentShield.php", "line": "121"}, "connection": "mcabplpnet", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from \"roles\" where (\"name\" = 'panel_user') limit 1", "type": "query", "params": [], "bindings": ["panel_user"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, {"index": 21, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Support/Utils.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Support\\Utils.php", "line": 114}, {"index": 22, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasPanelShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasPanelShield.php", "line": 17}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 208}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 54}], "start": **********.032742, "duration": 0.03073, "duration_str": "30.73ms", "memory": 0, "memory_str": null, "filename": "FilamentShield.php:121", "source": {"index": 20, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FFilamentShield.php&line=121", "ajax": false, "filename": "FilamentShield.php", "line": "121"}, "connection": "mcabplpnet", "explain": null, "start_percent": 0, "width_percent": 90.81}, {"sql": "select * from \"users\" where \"id\" = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.064244, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "mcabplpnet", "explain": null, "start_percent": 90.81, "width_percent": 1.921}, {"sql": "select * from \"libraries\" where \"libraries\".\"deleted_at\" is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Livewire/InteractiveMap.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Livewire\\InteractiveMap.php", "line": 22}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.0828438, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "InteractiveMap.php:22", "source": {"index": 15, "namespace": null, "name": "app/Livewire/InteractiveMap.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Livewire\\InteractiveMap.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FLivewire%2FInteractiveMap.php&line=22", "ajax": false, "filename": "InteractiveMap.php", "line": "22"}, "connection": "mcabplpnet", "explain": null, "start_percent": 92.73, "width_percent": 3.369}, {"sql": "select * from \"wilayas\" where \"wilayas\".\"id\" in (1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20, 21, 22, 23, 25, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Livewire/InteractiveMap.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Livewire\\InteractiveMap.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.086004, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "InteractiveMap.php:22", "source": {"index": 20, "namespace": null, "name": "app/Livewire/InteractiveMap.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Livewire\\InteractiveMap.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FLivewire%2FInteractiveMap.php&line=22", "ajax": false, "filename": "InteractiveMap.php", "line": "22"}, "connection": "mcabplpnet", "explain": null, "start_percent": 96.099, "width_percent": 3.901}]}, "models": {"data": {"App\\Models\\Library": {"value": 45, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FModels%2FLibrary.php&line=1", "ajax": false, "filename": "Library.php", "line": "?"}}, "App\\Models\\Wilaya": {"value": 44, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FModels%2FWilaya.php&line=1", "ajax": false, "filename": "Wilaya.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 91, "is_counter": true}, "livewire": {"data": {"app.filament.widgets.algeria-map-widget #44CzyUP4CIhHN0qr68QQ": "array:4 [\n  \"data\" => []\n  \"name\" => \"app.filament.widgets.algeria-map-widget\"\n  \"component\" => \"App\\Filament\\Widgets\\AlgeriaMapWidget\"\n  \"id\" => \"44CzyUP4CIhHN0qr68QQ\"\n]", "interactive-map #B8vR8avlNBv45tcy3GqV": "array:4 [\n  \"data\" => array:6 [\n    \"libraries\" => Illuminate\\Database\\Eloquent\\Collection {#2380\n      #items: array:45 [\n        0 => App\\Models\\Library {#2560\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [\n            \"id\" => 1\n            \"name\" => \" تمنراست\"\n            \"minister\" => null\n            \"wilaya_id\" => \"11\"\n            \"municipality_id\" => null\n            \"code\" => \"1101\"\n            \"status\" => \"Open\"\n            \"agent_name\" => \"بن دايس عبد الوهاب\"\n            \"ip_adress\" => \"*************\"\n            \"api_port\" => \"4438\"\n            \"api_end\" => \"/api\"\n            \"books_end_point\" => \"/api/books\"\n            \"readers_end_point\" => \"/api/reader\"\n            \"stats_end_point\" => \"/api/stasts\"\n            \"api_version\" => \"1\"\n            \"software\" => \"pmb\"\n            \"adress\" => null\n            \"phone\" => null\n            \"fax\" => null\n            \"email\" => null\n            \"website_url\" => null\n            \"facebook_url\" => null\n            \"notes\" => \"قيد التجريب\"\n            \"created_at\" => null\n            \"updated_at\" => \"2025-01-13 06:51:17\"\n            \"deleted_at\" => null\n          ]\n          #original: array:26 [\n            \"id\" => 1\n            \"name\" => \" تمنراست\"\n            \"minister\" => null\n            \"wilaya_id\" => \"11\"\n            \"municipality_id\" => null\n            \"code\" => \"1101\"\n            \"status\" => \"Open\"\n            \"agent_name\" => \"بن دايس عبد الوهاب\"\n            \"ip_adress\" => \"*************\"\n            \"api_port\" => \"4438\"\n            \"api_end\" => \"/api\"\n            \"books_end_point\" => \"/api/books\"\n            \"readers_end_point\" => \"/api/reader\"\n            \"stats_end_point\" => \"/api/stasts\"\n            \"api_version\" => \"1\"\n            \"software\" => \"pmb\"\n            \"adress\" => null\n            \"phone\" => null\n            \"fax\" => null\n            \"email\" => null\n            \"website_url\" => null\n            \"facebook_url\" => null\n            \"notes\" => \"قيد التجريب\"\n            \"created_at\" => null\n            \"updated_at\" => \"2025-01-13 06:51:17\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [\n            \"wilaya\" => App\\Models\\Wilaya {#2609 …36}\n          ]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [\n            0 => \"name\"\n            1 => \"code\"\n            2 => \"ip_adress\"\n            3 => \"api_port\"\n            4 => \"api_end\"\n            5 => \"books_end_point\"\n            6 => \"readers_end_point\"\n            7 => \"stats_end_point\"\n            8 => \"api_version\"\n            9 => \"status\"\n            10 => \"agent_name\"\n            11 => \"software\"\n            12 => \"wilaya_id\"\n            13 => \"municipality_id\"\n            14 => \"minister\"\n            15 => \"adress\"\n            16 => \"phone\"\n            17 => \"fax\"\n            18 => \"email\"\n            19 => \"website_url\"\n            20 => \"facebook_url\"\n            21 => \"notes\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: array:3 [\n            0 => \"created_at\"\n            1 => \"updated_at\"\n            2 => \"deleted_at\"\n          ]\n          +orderable: array:22 [\n            0 => \"id\"\n            1 => \"name\"\n            2 => \"code\"\n            3 => \"ip_adress\"\n            4 => \"api_port\"\n            5 => \"api_end\"\n            6 => \"books_end_point\"\n            7 => \"readers_end_point\"\n            8 => \"stats_end_point\"\n            9 => \"api_version\"\n            10 => \"software\"\n            11 => \"wilaya_id\"\n            12 => \"municipality_id\"\n            13 => \"minister\"\n            14 => \"adress\"\n            15 => \"status\"\n            16 => \"phone\"\n            17 => \"fax\"\n            18 => \"email\"\n            19 => \"website_url\"\n            20 => \"facebook_url\"\n            21 => \"notes\"\n          ]\n          +filterable: array:22 [\n            0 => \"id\"\n            1 => \"name\"\n            2 => \"code\"\n            3 => \"ip_adress\"\n            4 => \"api_port\"\n            5 => \"api_end\"\n            6 => \"books_end_point\"\n            7 => \"readers_end_point\"\n            8 => \"stats_end_point\"\n            9 => \"api_version\"\n            10 => \"software\"\n            11 => \"wilaya_id\"\n            12 => \"municipality_id\"\n            13 => \"minister\"\n            14 => \"adress\"\n            15 => \"status\"\n            16 => \"phone\"\n            17 => \"fax\"\n            18 => \"email\"\n            19 => \"website_url\"\n            20 => \"facebook_url\"\n            21 => \"notes\"\n          ]\n          #forceDeleting: false\n        }\n        1 => App\\Models\\Library {#2558\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [\n            \"id\" => 2\n            \"name\" => \" ادرار\"\n            \"minister\" => null\n            \"wilaya_id\" => \"1\"\n            \"municipality_id\" => \"1\"\n            \"code\" => \"0101\"\n            \"status\" => \"Open\"\n            \"agent_name\" => \"خالي علي مراد\"\n            \"ip_adress\" => \"*************\"\n            \"api_port\" => \"80\"\n            \"api_end\" => \"/api\"\n            \"books_end_point\" => \"/api/books\"\n            \"readers_end_point\" => \"/api/reader\"\n            \"stats_end_point\" => \"/api/stasts\"\n            \"api_version\" => \"1\"\n            \"software\" => \"fennec/syngeb\"\n            \"adress\" => null\n            \"phone\" => null\n            \"fax\" => null\n            \"email\" => null\n            \"website_url\" => null\n            \"facebook_url\" => null\n            \"notes\" => null\n            \"created_at\" => null\n            \"updated_at\" => \"2025-04-17 09:44:24\"\n            \"deleted_at\" => null\n          ]\n          #original: array:26 [\n            \"id\" => 2\n            \"name\" => \" ادرار\"\n            \"minister\" => null\n            \"wilaya_id\" => \"1\"\n            \"municipality_id\" => \"1\"\n            \"code\" => \"0101\"\n            \"status\" => \"Open\"\n            \"agent_name\" => \"خالي علي مراد\"\n            \"ip_adress\" => \"*************\"\n            \"api_port\" => \"80\"\n            \"api_end\" => \"/api\"\n            \"books_end_point\" => \"/api/books\"\n            \"readers_end_point\" => \"/api/reader\"\n            \"stats_end_point\" => \"/api/stasts\"\n            \"api_version\" => \"1\"\n            \"software\" => \"fennec/syngeb\"\n            \"adress\" => null\n            \"phone\" => null\n            \"fax\" => null\n            \"email\" => null\n            \"website_url\" => null\n            \"facebook_url\" => null\n            \"notes\" => null\n            \"created_at\" => null\n            \"updated_at\" => \"2025-04-17 09:44:24\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [\n            \"wilaya\" => App\\Models\\Wilaya {#2587 …36}\n          ]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [\n            0 => \"name\"\n            1 => \"code\"\n            2 => \"ip_adress\"\n            3 => \"api_port\"\n            4 => \"api_end\"\n            5 => \"books_end_point\"\n            6 => \"readers_end_point\"\n            7 => \"stats_end_point\"\n            8 => \"api_version\"\n            9 => \"status\"\n            10 => \"agent_name\"\n            11 => \"software\"\n            12 => \"wilaya_id\"\n            13 => \"municipality_id\"\n            14 => \"minister\"\n            15 => \"adress\"\n            16 => \"phone\"\n            17 => \"fax\"\n            18 => \"email\"\n            19 => \"website_url\"\n            20 => \"facebook_url\"\n            21 => \"notes\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: array:3 [\n            0 => \"created_at\"\n            1 => \"updated_at\"\n            2 => \"deleted_at\"\n          ]\n          +orderable: array:22 [\n            0 => \"id\"\n            1 => \"name\"\n            2 => \"code\"\n            3 => \"ip_adress\"\n            4 => \"api_port\"\n            5 => \"api_end\"\n            6 => \"books_end_point\"\n            7 => \"readers_end_point\"\n            8 => \"stats_end_point\"\n            9 => \"api_version\"\n            10 => \"software\"\n            11 => \"wilaya_id\"\n            12 => \"municipality_id\"\n            13 => \"minister\"\n            14 => \"adress\"\n            15 => \"status\"\n            16 => \"phone\"\n            17 => \"fax\"\n            18 => \"email\"\n            19 => \"website_url\"\n            20 => \"facebook_url\"\n            21 => \"notes\"\n          ]\n          +filterable: array:22 [\n            0 => \"id\"\n            1 => \"name\"\n            2 => \"code\"\n            3 => \"ip_adress\"\n            4 => \"api_port\"\n            5 => \"api_end\"\n            6 => \"books_end_point\"\n            7 => \"readers_end_point\"\n            8 => \"stats_end_point\"\n            9 => \"api_version\"\n            10 => \"software\"\n            11 => \"wilaya_id\"\n            12 => \"municipality_id\"\n            13 => \"minister\"\n            14 => \"adress\"\n            15 => \"status\"\n            16 => \"phone\"\n            17 => \"fax\"\n            18 => \"email\"\n            19 => \"website_url\"\n            20 => \"facebook_url\"\n            21 => \"notes\"\n          ]\n          #forceDeleting: false\n        }\n        2 => App\\Models\\Library {#2557\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [\n            \"id\" => 3\n            \"name\" => \"تلمسان\"\n            \"minister\" => null\n            \"wilaya_id\" => \"13\"\n            \"municipality_id\" => \"359\"\n            \"code\" => \"1301\"\n            \"status\" => \"Open\"\n            \"agent_name\" => \"يوسف موساوي\"\n            \"ip_adress\" => \"*************\"\n            \"api_port\" => \"56658\"\n            \"api_end\" => \"/api\"\n            \"books_end_point\" => \"/api/books\"\n            \"readers_end_point\" => \"/api/reader\"\n            \"stats_end_point\" => \"/api/stats\"\n            \"api_version\" => \"1\"\n            \"software\" => \"PMB\"\n            \"adress\" => null\n            \"phone\" => null\n            \"fax\" => null\n            \"email\" => null\n            \"website_url\" => null\n            \"facebook_url\" => null\n            \"notes\" => null\n            \"created_at\" => null\n            \"updated_at\" => \"2025-05-06 06:53:02\"\n            \"deleted_at\" => null\n          ]\n          #original: array:26 [\n            \"id\" => 3\n            \"name\" => \"تلمسان\"\n            \"minister\" => null\n            \"wilaya_id\" => \"13\"\n            \"municipality_id\" => \"359\"\n            \"code\" => \"1301\"\n            \"status\" => \"Open\"\n            \"agent_name\" => \"يوسف موساوي\"\n            \"ip_adress\" => \"*************\"\n            \"api_port\" => \"56658\"\n            \"api_end\" => \"/api\"\n            \"books_end_point\" => \"/api/books\"\n            \"readers_end_point\" => \"/api/reader\"\n            \"stats_end_point\" => \"/api/stats\"\n            \"api_version\" => \"1\"\n            \"software\" => \"PMB\"\n            \"adress\" => null\n            \"phone\" => null\n            \"fax\" => null\n            \"email\" => null\n            \"website_url\" => null\n            \"facebook_url\" => null\n            \"notes\" => null\n            \"created_at\" => null\n            \"updated_at\" => \"2025-05-06 06:53:02\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [\n            \"wilaya\" => App\\Models\\Wilaya {#2607 …36}\n          ]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [\n            0 => \"name\"\n            1 => \"code\"\n            2 => \"ip_adress\"\n            3 => \"api_port\"\n            4 => \"api_end\"\n            5 => \"books_end_point\"\n            6 => \"readers_end_point\"\n            7 => \"stats_end_point\"\n            8 => \"api_version\"\n            9 => \"status\"\n            10 => \"agent_name\"\n            11 => \"software\"\n            12 => \"wilaya_id\"\n            13 => \"municipality_id\"\n            14 => \"minister\"\n            15 => \"adress\"\n            16 => \"phone\"\n            17 => \"fax\"\n            18 => \"email\"\n            19 => \"website_url\"\n            20 => \"facebook_url\"\n            21 => \"notes\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: array:3 [\n            0 => \"created_at\"\n            1 => \"updated_at\"\n            2 => \"deleted_at\"\n          ]\n          +orderable: array:22 [\n            0 => \"id\"\n            1 => \"name\"\n            2 => \"code\"\n            3 => \"ip_adress\"\n            4 => \"api_port\"\n            5 => \"api_end\"\n            6 => \"books_end_point\"\n            7 => \"readers_end_point\"\n            8 => \"stats_end_point\"\n            9 => \"api_version\"\n            10 => \"software\"\n            11 => \"wilaya_id\"\n            12 => \"municipality_id\"\n            13 => \"minister\"\n            14 => \"adress\"\n            15 => \"status\"\n            16 => \"phone\"\n            17 => \"fax\"\n            18 => \"email\"\n            19 => \"website_url\"\n            20 => \"facebook_url\"\n            21 => \"notes\"\n          ]\n          +filterable: array:22 [\n            0 => \"id\"\n            1 => \"name\"\n            2 => \"code\"\n            3 => \"ip_adress\"\n            4 => \"api_port\"\n            5 => \"api_end\"\n            6 => \"books_end_point\"\n            7 => \"readers_end_point\"\n            8 => \"stats_end_point\"\n            9 => \"api_version\"\n            10 => \"software\"\n            11 => \"wilaya_id\"\n            12 => \"municipality_id\"\n            13 => \"minister\"\n            14 => \"adress\"\n            15 => \"status\"\n            16 => \"phone\"\n            17 => \"fax\"\n            18 => \"email\"\n            19 => \"website_url\"\n            20 => \"facebook_url\"\n            21 => \"notes\"\n          ]\n          #forceDeleting: false\n        }\n        3 => App\\Models\\Library {#2556\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [\n            \"id\" => 4\n            \"name\" => \" تيندوف\"\n            \"minister\" => null\n            \"wilaya_id\" => \"37\"\n            \"municipality_id\" => null\n            \"code\" => \"3701\"\n            \"status\" => \"Open\"\n            \"agent_name\" => \"بلولة لخضر\"\n            \"ip_adress\" => \"*************\"\n            \"api_port\" => \"45380\"\n            \"api_end\" => \"/api\"\n            \"books_end_point\" => \"/api/books\"\n            \"readers_end_point\" => \"/api/reader\"\n            \"stats_end_point\" => \"/api/stasts\"\n            \"api_version\" => null\n            \"software\" => \"pmb\"\n            \"adress\" => null\n            \"phone\" => null\n            \"fax\" => null\n            \"email\" => null\n            \"website_url\" => null\n            \"facebook_url\" => null\n            \"notes\" => \"يتم تجريب الربط\"\n            \"created_at\" => null\n            \"updated_at\" => \"2025-01-13 06:51:35\"\n            \"deleted_at\" => null\n          ]\n          #original: array:26 [\n            \"id\" => 4\n            \"name\" => \" تيندوف\"\n            \"minister\" => null\n            \"wilaya_id\" => \"37\"\n            \"municipality_id\" => null\n            \"code\" => \"3701\"\n            \"status\" => \"Open\"\n            \"agent_name\" => \"بلولة لخضر\"\n            \"ip_adress\" => \"*************\"\n            \"api_port\" => \"45380\"\n            \"api_end\" => \"/api\"\n            \"books_end_point\" => \"/api/books\"\n            \"readers_end_point\" => \"/api/reader\"\n            \"stats_end_point\" => \"/api/stasts\"\n            \"api_version\" => null\n            \"software\" => \"pmb\"\n            \"adress\" => null\n            \"phone\" => null\n            \"fax\" => null\n            \"email\" => null\n            \"website_url\" => null\n            \"facebook_url\" => null\n            \"notes\" => \"يتم تجريب الربط\"\n            \"created_at\" => null\n            \"updated_at\" => \"2025-01-13 06:51:35\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [\n            \"wilaya\" => App\\Models\\Wilaya {#2624 …36}\n          ]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [\n            0 => \"name\"\n            1 => \"code\"\n            2 => \"ip_adress\"\n            3 => \"api_port\"\n            4 => \"api_end\"\n            5 => \"books_end_point\"\n            6 => \"readers_end_point\"\n            7 => \"stats_end_point\"\n            8 => \"api_version\"\n            9 => \"status\"\n            10 => \"agent_name\"\n            11 => \"software\"\n            12 => \"wilaya_id\"\n            13 => \"municipality_id\"\n            14 => \"minister\"\n            15 => \"adress\"\n            16 => \"phone\"\n            17 => \"fax\"\n            18 => \"email\"\n            19 => \"website_url\"\n            20 => \"facebook_url\"\n            21 => \"notes\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: array:3 [\n            0 => \"created_at\"\n            1 => \"updated_at\"\n            2 => \"deleted_at\"\n          ]\n          +orderable: array:22 [\n            0 => \"id\"\n            1 => \"name\"\n            2 => \"code\"\n            3 => \"ip_adress\"\n            4 => \"api_port\"\n            5 => \"api_end\"\n            6 => \"books_end_point\"\n            7 => \"readers_end_point\"\n            8 => \"stats_end_point\"\n            9 => \"api_version\"\n            10 => \"software\"\n            11 => \"wilaya_id\"\n            12 => \"municipality_id\"\n            13 => \"minister\"\n            14 => \"adress\"\n             …7\n          ]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        4 => App\\Models\\Library {#2555\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        5 => App\\Models\\Library {#2554\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        6 => App\\Models\\Library {#2553\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        7 => App\\Models\\Library {#2552\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        8 => App\\Models\\Library {#2551\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        9 => App\\Models\\Library {#2550\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        10 => App\\Models\\Library {#2549\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        11 => App\\Models\\Library {#2548\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        12 => App\\Models\\Library {#2546\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        13 => App\\Models\\Library {#2545\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        14 => App\\Models\\Library {#2544\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        15 => App\\Models\\Library {#2543\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        16 => App\\Models\\Library {#2542\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        17 => App\\Models\\Library {#2541\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        18 => App\\Models\\Library {#2540\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        19 => App\\Models\\Library {#2539\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        20 => App\\Models\\Library {#2538\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        21 => App\\Models\\Library {#2537\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        22 => App\\Models\\Library {#2536\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        23 => App\\Models\\Library {#2565\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        24 => App\\Models\\Library {#2566\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        25 => App\\Models\\Library {#2567\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        26 => App\\Models\\Library {#2568\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        27 => App\\Models\\Library {#2569\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        28 => App\\Models\\Library {#2570\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        29 => App\\Models\\Library {#2571\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        30 => App\\Models\\Library {#2572\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        31 => App\\Models\\Library {#2573\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        32 => App\\Models\\Library {#2574\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        33 => App\\Models\\Library {#2575\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        34 => App\\Models\\Library {#2576\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        35 => App\\Models\\Library {#2577\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        36 => App\\Models\\Library {#2578\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        37 => App\\Models\\Library {#2579\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        38 => App\\Models\\Library {#2580\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        39 => App\\Models\\Library {#2581\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        40 => App\\Models\\Library {#2582\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        41 => App\\Models\\Library {#2583\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        42 => App\\Models\\Library {#2584\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        43 => App\\Models\\Library {#2585\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        44 => App\\Models\\Library {#2586\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"hoveredLibrary\" => null\n    \"libraryDetails\" => array:45 [\n      1101 => array:6 [\n        \"name\" => \" تمنراست\"\n        \"code\" => \"1101\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/1\"\n      ]\n      \"0101\" => array:6 [\n        \"name\" => \" ادرار\"\n        \"code\" => \"0101\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/2\"\n      ]\n      1301 => array:6 [\n        \"name\" => \"تلمسان\"\n        \"code\" => \"1301\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/3\"\n      ]\n      3701 => array:6 [\n        \"name\" => \" تيندوف\"\n        \"code\" => \"3701\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/4\"\n      ]\n      1701 => array:6 [\n        \"name\" => \"الجلفة\"\n        \"code\" => \"1701\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/5\"\n      ]\n      2101 => array:6 [\n        \"name\" => \"سكيكدة\"\n        \"code\" => \"2101\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/6\"\n      ]\n      1201 => array:6 [\n        \"name\" => \"تبسة\"\n        \"code\" => \"1201\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/7\"\n      ]\n      \"0301\" => array:6 [\n        \"name\" => \"الاغواط\"\n        \"code\" => \"0301\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/8\"\n      ]\n      2001 => array:6 [\n        \"name\" => \"سعيدة\"\n        \"code\" => \"2001\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/9\"\n      ]\n      3301 => array:6 [\n        \"name\" => \"ايليزي\"\n        \"code\" => \"3301\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/10\"\n      ]\n      \"0401\" => array:6 [\n        \"name\" => \" ام البواقي\"\n        \"code\" => \"0401\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/11\"\n      ]\n      \"0701\" => array:6 [\n        \"name\" => \"بسكرة\"\n        \"code\" => \"0701\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/12\"\n      ]\n      1801 => array:6 [\n        \"name\" => \"جيجل\"\n        \"code\" => \"1801\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/13\"\n      ]\n      2501 => array:6 [\n        \"name\" => \"قسنطينة\"\n        \"code\" => \"2501\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/14\"\n      ]\n      3501 => array:6 [\n        \"name\" => \"بومرداس\"\n        \"code\" => \"3501\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/15\"\n      ]\n      4001 => array:6 [\n        \"name\" => \"خنشلة\"\n        \"code\" => \"4001\"\n        \"status\" => \"widgets.closed\"\n        \"wilaya\" => null\n        \"status_color\" => \"#ff6060\"\n        \"link\" => \"http://bplpnet.test/libraries/16\"\n      ]\n      4701 => array:6 [\n        \"name\" => \"غرداية\"\n        \"code\" => \"4701\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/17\"\n      ]\n      3201 => array:6 [\n        \"name\" => \"البيض\"\n        \"code\" => \"3201\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/18\"\n      ]\n      4501 => array:6 [\n        \"name\" => \"النعامة\"\n        \"code\" => \"4501\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/19\"\n      ]\n      4401 => array:6 [\n        \"name\" => \"عين الدفلة\"\n        \"code\" => \"4401\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/20\"\n      ]\n      \"0501\" => array:6 [\n        \"name\" => \"باتنة\"\n        \"code\" => \"0501\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/21\"\n      ]\n      4801 => array:6 [\n        \"name\" => \"غيليزان\"\n        \"code\" => \"4801\"\n        \"status\" => \"widgets.closed\"\n        \"wilaya\" => null\n        \"status_color\" => \"#ff6060\"\n        \"link\" => \"http://bplpnet.test/libraries/22\"\n      ]\n      1501 => array:6 [\n        \"name\" => \"تيزي وزو\"\n        \"code\" => \"1501\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/23\"\n      ]\n      4101 => array:6 [\n        \"name\" => \"سوق اهراس\"\n        \"code\" => \"4101\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/24\"\n      ]\n      4301 => array:6 [\n        \"name\" => \"ميلة\"\n        \"code\" => \"4301\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/25\"\n      ]\n      1401 => array:6 [\n        \"name\" => \"تيارت\"\n        \"code\" => \"1401\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/26\"\n      ]\n      3401 => array:6 [\n        \"name\" => \"بورج بوعريريج\"\n        \"code\" => \"3401\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/27\"\n      ]\n      3001 => array:6 [\n        \"name\" => \"ورقلة\"\n        \"code\" => \"3001\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/28\"\n      ]\n      1001 => array:6 [\n        \"name\" => \"البويرة\"\n        \"code\" => \"1001\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/29\"\n      ]\n      3901 => array:6 [\n        \"name\" => \"الوادي\"\n        \"code\" => \"3901\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/30\"\n      ]\n      3601 => array:6 [\n        \"name\" => \"الطارف\"\n        \"code\" => \"3601\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/31\"\n      ]\n      2201 => array:6 [\n        \"name\" => \"سيدي بلعباس\"\n        \"code\" => \"2201\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/32\"\n      ]\n      4201 => array:6 [\n        \"name\" => \"تيبازة\"\n        \"code\" => \"4201\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/33\"\n      ]\n      3801 => array:6 [\n        \"name\" => \"تسمسيلت\"\n        \"code\" => \"3801\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/34\"\n      ]\n      2601 => array:6 [\n        \"name\" => \"المدية\"\n        \"code\" => \"2601\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/35\"\n      ]\n      2901 => array:6 [\n        \"name\" => \"معسكر\"\n        \"code\" => \"2901\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/36\"\n      ]\n      \"0801\" => array:6 [\n        \"name\" => \"بشار\"\n        \"code\" => \"0801\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/37\"\n      ]\n      \"0601\" => array:6 [\n        \"name\" => \"بجاية\"\n        \"code\" => \"0601\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/38\"\n      ]\n      4601 => array:6 [\n        \"name\" => \"عين تموشنت\"\n        \"code\" => \"4601\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/39\"\n      ]\n      2301 => array:6 [\n        \"name\" => \"عنابة\"\n        \"code\" => \"2301\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/40\"\n      ]\n      \"0201\" => array:6 [\n        \"name\" => \"الشلف\"\n        \"code\" => \"0201\"\n        \"status\" => \"widgets.closed\"\n        \"wilaya\" => null\n        \"status_color\" => \"#ff6060\"\n        \"link\" => \"http://bplpnet.test/libraries/41\"\n      ]\n      2801 => array:6 [\n        \"name\" => \"مسيلة\"\n        \"code\" => \"2801\"\n        \"status\" => \"widgets.closed\"\n        \"wilaya\" => null\n        \"status_color\" => \"#ff6060\"\n        \"link\" => \"http://bplpnet.test/libraries/42\"\n      ]\n      1600 => array:6 [\n        \"name\" => \"المكتبة الوطنية الجزائرية\"\n        \"code\" => \"1600\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/43\"\n      ]\n      2701 => array:6 [\n        \"name\" => \"مستغانم \"\n        \"code\" => \"2701\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/44\"\n      ]\n      1601 => array:6 [\n        \"name\" => \"الجزائر\"\n        \"code\" => \"1601\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/45\"\n      ]\n    ]\n    \"colorOpen\" => \"#22c55e\"\n    \"colorClosed\" => \"#ff6060\"\n    \"defaultSvgFill\" => \"#7c7c7c\"\n  ]\n  \"name\" => \"interactive-map\"\n  \"component\" => \"App\\Livewire\\InteractiveMap\"\n  \"id\" => \"B8vR8avlNBv45tcy3GqV\"\n]"}, "count": 2}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG", "PHPDEBUGBAR_STACK_DATA": "[]", "_previous": "array:1 [\n  \"url\" => \"http://bplpnet.test/admin\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "23", "password_hash_web": "$2y$10$ndp0mY0vfh7SKC/wbKghcOK8n.7TdyC6/Z5j84PViILPym72FiJWy", "48040ef7f2542b39b9ba9a72983b0d88_filters": "array:3 [\n  \"library\" => null\n  \"startDate\" => null\n  \"endDate\" => null\n]"}, "request": {"data": {"status": "200 OK", "full_url": "http://bplpnet.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "telescope": "<a href=\"http://bplpnet.test/_debugbar/telescope/9f4ce0a4-9884-4f8e-b0e3-911a4198dbd7\" target=\"_blank\">View in Telescope</a>", "duration": "408ms", "peak_memory": "44MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-339179924 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-339179924\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-75880963 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"309 characters\">{&quot;data&quot;:[],&quot;memo&quot;:{&quot;id&quot;:&quot;44CzyUP4CIhHN0qr68QQ&quot;,&quot;name&quot;:&quot;app.filament.widgets.algeria-map-widget&quot;,&quot;path&quot;:&quot;admin&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;lazyLoaded&quot;:false,&quot;lazyIsolated&quot;:true,&quot;errors&quot;:[],&quot;locale&quot;:&quot;ar&quot;},&quot;checksum&quot;:&quot;38140cb4943334ec483de0c7193d519730d457f9a945824151fbef467b7722d6&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"10 characters\">__lazyLoad</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"344 characters\">eyJkYXRhIjp7ImZvck1vdW50IjpbeyJmaWx0ZXJzIjpbeyJsaWJyYXJ5IjpudWxsLCJzdGFydERhdGUiOm51bGwsImVuZERhdGUiOm51bGx9LHsicyI6ImFyciJ9XX0seyJzIjoiYXJyIn1dfSwibWVtbyI6eyJpZCI6InhFNkxGYVUzZlBhU2VucWxuaHNIIiwibmFtZSI6Il9fbW91bnRQYXJhbXNDb250YWluZXIifSwiY2hlY2tzdW0iOiIyNjcxNjMyYzJjZWFlNWZlNTFkYzQ1MmRhMDkyYjk3ZDAxOGFiNzUxMTM5YTdlYWEzNzhkNDkzNjFmOGNmNzQxIn0=</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-75880963\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1555 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjhlNFJweU9qTWYyaXc4SEtWMDl3OGc9PSIsInZhbHVlIjoibnJRVzFVMC9nV09rS2xUUjNERDZJVG9TVlM3M3Q1RXU0S3hVMW9QRVRpMlQrc25qSm9UbFIwV1kzSzNqTUtaVU1xNExkV09NZHFLSjIyR1M3NTB1enJzbDI2bmRabmFON0VYSlN5OTFaMUdDUVVUSmMyYW15RmtjSmhtUmgyZVkzclRrTXd6eWpKbG5DUzFPY0JmaE1KQzRDVjUzQkp4Ukl4aUwwOXlRZXN6dllPL2dPbWllZE42QTRKbGFuczNMNVFENmlsaXVZb3lmcHNkZkRMejJqZDQzSHd3V3N2cU13MDJKbFJoZ0RkST0iLCJtYWMiOiI2NTRlYzEyZTY5ODY1MzRlMmY4YmM5MTM4MTdhN2Q3YWQwYzQzZTZiOGQ3YmFmZDg3YWFjMzJhMTgyYzBiMTc3IiwidGFnIjoiIn0%3D; filament_language_switch_locale=eyJpdiI6IldSalJ4dW1CcFhqWCtSR2pFSytLYUE9PSIsInZhbHVlIjoiQTVEaTRtaXhMVlhxNTBXZEUrOSt3UDVoQUJvOGdCa21ZODBwekJ5aDc5bjJ2SzgySVR1YnpMOVNWWlpSdjVWcyIsIm1hYyI6ImUyNjI0NmUyMzk3MjI2MmUyNzdiY2JhOGVhMzE0YjRjOWIyNzJiODUxMGU1NzQzYTJhY2M1YjIwYjc0ZTJkOTgiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6IkgyUWlMMEJaMDFhdzBWaUR5UzFEMWc9PSIsInZhbHVlIjoiaDF5Zmp0RVVUeW5LcXU0dFVHWVBWVjAyRmdaelJ1STc1dXV5ejlENlVoRHNCR0N6RGtxOFlwYkFqdkRCcTFGOUpyM2VpT3R1V1BuRkZUdEcxQmtTS01sYUhrV2ZZMkQ4UTZoWXlxNGlZVExaM2JUdWNsTDlJZTQ2OGZlcDB1aG4iLCJtYWMiOiI2ZTdkNWM1YjEwNDg5ZWFiZmE1MjE4Nzc1M2ZjZWIxZjg4MGFkMjNmYmMzZTllZjYyODEwODI3MjgyNTAxYTFhIiwidGFnIjoiIn0%3D; alfhrs_almohd_session=eyJpdiI6InVUUlVqcW1hWk9QNkZsbFlqWUc3YVE9PSIsInZhbHVlIjoidDlLYXVWdHRaSFcyVkpvT0V1NmJrd3N3THUvWUZrZ0tpb3JqZzhrRmtoNFVqek9aanpCcmVGbWU5ODJNVU5ReXpYT2tjdGxoR2tSYWM0UmJGZTEwUHNRTGdHV044U2wwbDFnY0Nna2s4c0lCV3NUVUdZWHorMXZxVktWdCt4elMiLCJtYWMiOiJhYmEzNjFmZmMwMzgwNzMwZWY0M2IyMzk4NTg5ZGE1OWVmYmQ3MTU5YTNkZWUzNjNkODViYmU2ZTllMzljODY4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">ar-EG,ar;q=0.9,en-US;q=0.8,en;q=0.7,fr;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://bplpnet.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">http://bplpnet.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">848</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">bplpnet.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-916857087 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"124 characters\">23|oayZmX4hCkVRzDYgX8XiyDlbdUWGhjg8WRylkmFAigbARBfuImkVfTcPvqxq|$2y$10$ndp0mY0vfh7SKC/wbKghcOK8n.7TdyC6/Z5j84PViILPym72FiJWy</span>\"\n  \"<span class=sf-dump-key>filament_language_switch_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG</span>\"\n  \"<span class=sf-dump-key>alfhrs_almohd_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OHrIdfK5jqy65dnTOAgZp53RllI0Bol0hjZv4x8G</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-916857087\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1878394485 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 03 Jul 2025 06:49:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1878394485\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1405123911 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://bplpnet.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$10$ndp0mY0vfh7SKC/wbKghcOK8n.7TdyC6/Z5j84PViILPym72FiJWy</span>\"\n  \"<span class=sf-dump-key>48040ef7f2542b39b9ba9a72983b0d88_filters</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>library</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>startDate</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>endDate</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1405123911\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://bplpnet.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}