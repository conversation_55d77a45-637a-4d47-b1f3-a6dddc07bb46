{"__meta": {"id": "01JZ7HQFQ6PFHH9Z0FMRGMHMW2", "datetime": "2025-07-03 08:06:31", "utime": **********.527715, "method": "GET", "uri": "/admin", "ip": "127.0.0.1"}, "php": {"version": "8.4.4", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751526390.799047, "end": **********.527758, "duration": 0.7287108898162842, "duration_str": "729ms", "measures": [{"label": "Booting", "start": 1751526390.799047, "relative_start": 0, "end": **********.215594, "relative_end": **********.215594, "duration": 0.****************, "duration_str": "417ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.215608, "relative_start": 0.****************, "end": **********.527761, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "312ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.230946, "relative_start": 0.****************, "end": **********.234268, "relative_end": **********.234268, "duration": 0.0033218860626220703, "duration_str": "3.32ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.522728, "relative_start": 0.****************, "end": **********.522937, "relative_end": **********.522937, "duration": 0.0002090930938720703, "duration_str": "209μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.524539, "relative_start": 0.***************, "end": **********.524605, "relative_end": **********.524605, "duration": 6.604194641113281e-05, "duration_str": "66μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "route": {"uri": "GET admin", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, BezhanSalleh\\FilamentLanguageSwitch\\Http\\Middleware\\SwitchLanguageLocale, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Filament\\Http\\Middleware\\Authenticate", "uses": "App\\Filament\\Pages\\Dashboard@__invoke", "controller": "App\\Filament\\Pages\\Dashboard@render<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "filament.admin.pages.dashboard", "prefix": "admin/", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>"}, "queries": {"count": 9, "nb_statements": 8, "nb_visible_statements": 9, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.04584999999999999, "accumulated_duration_str": "45.85ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, {"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Support/Utils.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Support\\Utils.php", "line": 114}, {"index": 17, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasPanelShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasPanelShield.php", "line": 17}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 208}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 54}], "start": **********.260227, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "FilamentShield.php:121", "source": {"index": 15, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FFilamentShield.php&line=121", "ajax": false, "filename": "FilamentShield.php", "line": "121"}, "connection": "mcabplpnet", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from \"roles\" where (\"name\" = 'panel_user') limit 1", "type": "query", "params": [], "bindings": ["panel_user"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, {"index": 21, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Support/Utils.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Support\\Utils.php", "line": 114}, {"index": 22, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasPanelShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasPanelShield.php", "line": 17}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 208}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 54}], "start": **********.260846, "duration": 0.03143, "duration_str": "31.43ms", "memory": 0, "memory_str": null, "filename": "FilamentShield.php:121", "source": {"index": 20, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FFilamentShield.php&line=121", "ajax": false, "filename": "FilamentShield.php", "line": "121"}, "connection": "mcabplpnet", "explain": null, "start_percent": 0, "width_percent": 68.55}, {"sql": "select * from \"users\" where \"id\" = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.293107, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "mcabplpnet", "explain": null, "start_percent": 68.55, "width_percent": 1.854}, {"sql": "select \"name\", \"id\" from \"libraries\" where \"status\" = 'Open' and \"libraries\".\"deleted_at\" is null", "type": "query", "params": [], "bindings": ["Open"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Pages\\Dashboard.php", "line": 27}, {"index": 15, "namespace": null, "name": "vendor/filament/filament/src/Pages/Dashboard/Concerns/HasFiltersForm.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Pages\\Dashboard\\Concerns\\HasFiltersForm.php", "line": 29}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Pages/Dashboard/Concerns/HasFiltersForm.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Pages\\Dashboard\\Concerns\\HasFiltersForm.php", "line": 14}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Concerns/InteractsWithForms.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\forms\\src\\Concerns\\InteractsWithForms.php", "line": 411}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Concerns/InteractsWithForms.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\forms\\src\\Concerns\\InteractsWithForms.php", "line": 365}], "start": **********.315839, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "Dashboard.php:27", "source": {"index": 14, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Pages\\Dashboard.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FPages%2FDashboard.php&line=27", "ajax": false, "filename": "Dashboard.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 70.403, "width_percent": 3.446}, {"sql": "select \"permissions\".*, \"model_has_permissions\".\"model_id\" as \"pivot_model_id\", \"model_has_permissions\".\"permission_id\" as \"pivot_permission_id\", \"model_has_permissions\".\"model_type\" as \"pivot_model_type\" from \"permissions\" inner join \"model_has_permissions\" on \"permissions\".\"id\" = \"model_has_permissions\".\"permission_id\" where \"model_has_permissions\".\"model_id\" in (23) and \"model_has_permissions\".\"model_type\" = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": **********.392239, "duration": 0.00228, "duration_str": "2.28ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "mcabplpnet", "explain": null, "start_percent": 73.85, "width_percent": 4.973}, {"sql": "select \"roles\".*, \"model_has_roles\".\"model_id\" as \"pivot_model_id\", \"model_has_roles\".\"role_id\" as \"pivot_role_id\", \"model_has_roles\".\"model_type\" as \"pivot_model_type\" from \"roles\" inner join \"model_has_roles\" on \"roles\".\"id\" = \"model_has_roles\".\"role_id\" where \"model_has_roles\".\"model_id\" in (23) and \"model_has_roles\".\"model_type\" = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 314}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}], "start": **********.3972058, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "mcabplpnet", "explain": null, "start_percent": 78.822, "width_percent": 2.835}, {"sql": "select count(*) as aggregate from \"roles\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Resources/RoleResource.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Resources\\RoleResource.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.409937, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:206", "source": {"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Resources/RoleResource.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Resources\\RoleResource.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FResources%2FRoleResource.php&line=206", "ajax": false, "filename": "RoleResource.php", "line": "206"}, "connection": "mcabplpnet", "explain": null, "start_percent": 81.658, "width_percent": 1.527}, {"sql": "select * from \"media\" where \"media\".\"model_id\" in (23) and \"media\".\"model_type\" = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 279}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 311}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 298}], "start": **********.451278, "duration": 0.0072, "duration_str": "7.2ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:635", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=635", "ajax": false, "filename": "InteractsWithMedia.php", "line": "635"}, "connection": "mcabplpnet", "explain": null, "start_percent": 83.184, "width_percent": 15.703}, {"sql": "select count(*) as aggregate from \"roles\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Resources/RoleResource.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Resources\\RoleResource.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.474171, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:206", "source": {"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Resources/RoleResource.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Resources\\RoleResource.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FResources%2FRoleResource.php&line=206", "ajax": false, "filename": "RoleResource.php", "line": "206"}, "connection": "mcabplpnet", "explain": null, "start_percent": 98.888, "width_percent": 1.112}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 3, "is_counter": true}, "livewire": {"data": {"app.filament.pages.dashboard #FHjciJ0An4ETjcBj4lXT": "array:4 [\n  \"data\" => array:16 [\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"areFormStateUpdateHooksDisabledForTesting\" => false\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"filters\" => array:3 [\n      \"library\" => null\n      \"startDate\" => null\n      \"endDate\" => null\n    ]\n  ]\n  \"name\" => \"app.filament.pages.dashboard\"\n  \"component\" => \"App\\Filament\\Pages\\Dashboard\"\n  \"id\" => \"FHjciJ0An4ETjcBj4lXT\"\n]", "filament-language-switch #WIZ3o2ihiy6a8BtA1tyd": "array:4 [\n  \"data\" => []\n  \"name\" => \"filament-language-switch\"\n  \"component\" => \"BezhanSalleh\\FilamentLanguageSwitch\\Http\\Livewire\\FilamentLanguageSwitch\"\n  \"id\" => \"WIZ3o2ihiy6a8BtA1tyd\"\n]", "filament.livewire.notifications #6OqRIFKHIa94xyIMRoHN": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#3211\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"6OqRIFKHIa94xyIMRoHN\"\n]"}, "count": 3}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 52, "messages": [{"message": "[\n  ability => page_FennecBackup,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1118454561 data-indent-pad=\"  \"><span class=sf-dump-note>page_FennecBackup </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">page_FennecBackup</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1118454561\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.400826, "xdebug_link": null}, {"message": "[\n  ability => page_FennecBackup,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1705178982 data-indent-pad=\"  \"><span class=sf-dump-note>page_FennecBackup </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">page_FennecBackup</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1705178982\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.401937, "xdebug_link": null}, {"message": "[\n  ability => page_BookSearchApiPage,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-846019097 data-indent-pad=\"  \"><span class=sf-dump-note>page_BookSearchApiPage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">page_BookSearchApiPage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-846019097\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.405209, "xdebug_link": null}, {"message": "[\n  ability => page_BookSearchApiPage,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2115300600 data-indent-pad=\"  \"><span class=sf-dump-note>page_BookSearchApiPage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">page_BookSearchApiPage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2115300600\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.405576, "xdebug_link": null}, {"message": "[\n  ability => page_SystemDashboard,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-745405296 data-indent-pad=\"  \"><span class=sf-dump-note>page_SystemDashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">page_SystemDashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-745405296\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.406493, "xdebug_link": null}, {"message": "[\n  ability => page_SystemDashboard,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-845549961 data-indent-pad=\"  \"><span class=sf-dump-note>page_SystemDashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">page_SystemDashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-845549961\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.406867, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1554148391 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1554148391\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.408504, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user => 23,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2003658618 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2003658618\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.40862, "xdebug_link": null}, {"message": "[\n  ability => view_any_monitored::scheduled::task,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1921159249 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_monitored::scheduled::task </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"35 characters\">view_any_monitored::scheduled::task</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1921159249\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.412831, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTask,\n  result => true,\n  user => 23,\n  arguments => [0 => Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTask]\n]", "message_html": "<pre class=sf-dump id=sf-dump-780915290 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTask</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"52 characters\">Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTask</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"59 characters\">[0 =&gt; Spa<PERSON>\\ScheduleMonitor\\Models\\MonitoredScheduledTask]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-780915290\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.412963, "xdebug_link": null}, {"message": "[\n  ability => view_any_monitored::scheduled::task::log::item,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-140148838 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_monitored::scheduled::task::log::item </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"46 characters\">view_any_monitored::scheduled::task::log::item</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-140148838\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.414836, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\ScheduleMonitor\\Models\\MonitoredScheduledTaskLogItem,\n  result => true,\n  user => 23,\n  arguments => [0 => <PERSON><PERSON>\\ScheduleMonitor\\Models\\MonitoredScheduledTaskLogItem]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTaskLogItem</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"59 characters\">Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTaskLogItem</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"66 characters\">[0 =&gt; Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTaskLogItem]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.414957, "xdebug_link": null}, {"message": "[\n  ability => view_any_api::health::check,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-690188377 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_api::health::check </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">view_any_api::health::check</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-690188377\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.416554, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ApiHealthCheck,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\ApiHealthCheck]\n]", "message_html": "<pre class=sf-dump id=sf-dump-738103602 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ApiHealthCheck</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\ApiHealthCheck</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; App\\Models\\ApiHealthCheck]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-738103602\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.416681, "xdebug_link": null}, {"message": "[\n  ability => view_any_document::record,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-356808544 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_document::record </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">view_any_document::record</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-356808544\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.418415, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\DocumentRecord,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\DocumentRecord]\n]", "message_html": "<pre class=sf-dump id=sf-dump-437830489 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\DocumentRecord</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\DocumentRecord</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; App\\Models\\DocumentRecord]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-437830489\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.418529, "xdebug_link": null}, {"message": "[\n  ability => view_any_library,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-766500865 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_library </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_library</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-766500865\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.419504, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Library,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\Library]\n]", "message_html": "<pre class=sf-dump id=sf-dump-569311297 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Library</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Library</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Library]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-569311297\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.419618, "xdebug_link": null}, {"message": "[\n  ability => view_any_library::stats,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2135813197 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_library::stats </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view_any_library::stats</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2135813197\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.420903, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\LibraryStats,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\LibraryStats]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1486468256 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\LibraryStats</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\LibraryStats</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\LibraryStats]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1486468256\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.421017, "xdebug_link": null}, {"message": "[\n  ability => view_any_municipality,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1560445703 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_municipality </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">view_any_municipality</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1560445703\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.422384, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Municipality,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\Municipality]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1857427217 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Municipality</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\Municipality</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\Municipality]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1857427217\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.422553, "xdebug_link": null}, {"message": "[\n  ability => view_any_user,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-669173325 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-669173325\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.42476, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-597721654 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-597721654\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.424957, "xdebug_link": null}, {"message": "[\n  ability => view_any_wilaya,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-72245262 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_wilaya </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_wilaya</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-72245262\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.427383, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Wilaya,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\Wilaya]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1418743673 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Wilaya</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Wilaya</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Wilaya]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1418743673\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.42752, "xdebug_link": null}, {"message": "[\n  ability => page_FennecBackup,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-940541089 data-indent-pad=\"  \"><span class=sf-dump-note>page_FennecBackup </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">page_FennecBackup</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-940541089\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.470354, "xdebug_link": null}, {"message": "[\n  ability => page_FennecBackup,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1168384942 data-indent-pad=\"  \"><span class=sf-dump-note>page_FennecBackup </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">page_FennecBackup</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1168384942\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.470725, "xdebug_link": null}, {"message": "[\n  ability => page_BookSearchApiPage,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1770603802 data-indent-pad=\"  \"><span class=sf-dump-note>page_BookSearchApiPage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">page_BookSearchApiPage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1770603802\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.471271, "xdebug_link": null}, {"message": "[\n  ability => page_BookSearchApiPage,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2008409789 data-indent-pad=\"  \"><span class=sf-dump-note>page_BookSearchApiPage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">page_BookSearchApiPage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2008409789\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.471609, "xdebug_link": null}, {"message": "[\n  ability => page_SystemDashboard,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1534329413 data-indent-pad=\"  \"><span class=sf-dump-note>page_SystemDashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">page_SystemDashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1534329413\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.472185, "xdebug_link": null}, {"message": "[\n  ability => page_SystemDashboard,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-685372982 data-indent-pad=\"  \"><span class=sf-dump-note>page_SystemDashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">page_SystemDashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-685372982\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.47279, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-807976861 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-807976861\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.473687, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user => 23,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-795091980 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-795091980\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.473819, "xdebug_link": null}, {"message": "[\n  ability => view_any_monitored::scheduled::task,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1184070843 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_monitored::scheduled::task </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"35 characters\">view_any_monitored::scheduled::task</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1184070843\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.47584, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTask,\n  result => true,\n  user => 23,\n  arguments => [0 => Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTask]\n]", "message_html": "<pre class=sf-dump id=sf-dump-882565648 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTask</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"52 characters\">Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTask</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"59 characters\">[0 =&gt; Spa<PERSON>\\ScheduleMonitor\\Models\\MonitoredScheduledTask]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-882565648\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.475914, "xdebug_link": null}, {"message": "[\n  ability => view_any_monitored::scheduled::task::log::item,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-291626445 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_monitored::scheduled::task::log::item </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"46 characters\">view_any_monitored::scheduled::task::log::item</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-291626445\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.47632, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\ScheduleMonitor\\Models\\MonitoredScheduledTaskLogItem,\n  result => true,\n  user => 23,\n  arguments => [0 => <PERSON><PERSON>\\ScheduleMonitor\\Models\\MonitoredScheduledTaskLogItem]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTaskLogItem</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"59 characters\">Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTaskLogItem</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"66 characters\">[0 =&gt; Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTaskLogItem]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.47638, "xdebug_link": null}, {"message": "[\n  ability => view_any_api::health::check,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-580163234 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_api::health::check </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">view_any_api::health::check</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-580163234\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.476756, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ApiHealthCheck,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\ApiHealthCheck]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ApiHealthCheck</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\ApiHealthCheck</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; App\\Models\\ApiHealthCheck]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.476893, "xdebug_link": null}, {"message": "[\n  ability => view_any_document::record,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>view_any_document::record </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">view_any_document::record</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.477647, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\DocumentRecord,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\DocumentRecord]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2087334068 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\DocumentRecord</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\DocumentRecord</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; App\\Models\\DocumentRecord]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2087334068\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.477759, "xdebug_link": null}, {"message": "[\n  ability => view_any_library,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1466338591 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_library </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_library</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1466338591\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.478395, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Library,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\Library]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1598831784 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Library</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Library</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Library]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1598831784\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.478542, "xdebug_link": null}, {"message": "[\n  ability => view_any_library::stats,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1497271861 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_library::stats </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view_any_library::stats</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1497271861\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.479238, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\LibraryStats,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\LibraryStats]\n]", "message_html": "<pre class=sf-dump id=sf-dump-869862340 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\LibraryStats</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\LibraryStats</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\LibraryStats]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-869862340\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.479355, "xdebug_link": null}, {"message": "[\n  ability => view_any_municipality,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-515237360 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_municipality </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">view_any_municipality</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-515237360\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.480071, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Municipality,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\Municipality]\n]", "message_html": "<pre class=sf-dump id=sf-dump-916587966 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Municipality</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\Municipality</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\Municipality]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-916587966\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.480191, "xdebug_link": null}, {"message": "[\n  ability => view_any_user,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1888592914 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1888592914\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.480964, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1267019791 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1267019791\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.481087, "xdebug_link": null}, {"message": "[\n  ability => view_any_wilaya,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1773963642 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_wilaya </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_wilaya</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1773963642\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.481924, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Wilaya,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\Wilaya]\n]", "message_html": "<pre class=sf-dump id=sf-dump-467829994 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Wilaya</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Wilaya</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Wilaya]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-467829994\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.482048, "xdebug_link": null}]}, "session": {"_token": "fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG", "PHPDEBUGBAR_STACK_DATA": "[]", "_previous": "array:1 [\n  \"url\" => \"http://bplpnet.test/admin\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "23", "password_hash_web": "$2y$10$ndp0mY0vfh7SKC/wbKghcOK8n.7TdyC6/Z5j84PViILPym72FiJWy", "48040ef7f2542b39b9ba9a72983b0d88_filters": "array:3 [\n  \"library\" => null\n  \"startDate\" => null\n  \"endDate\" => null\n]"}, "request": {"data": {"status": "200 OK", "full_url": "http://bplpnet.test/admin", "action_name": "filament.admin.pages.dashboard", "controller_action": "App\\Filament\\Pages\\Dashboard", "uri": "GET admin", "controller": "App\\Filament\\Pages\\Dashboard@render<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:admin, panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, BezhanSalleh\\FilamentLanguageSwitch\\Http\\Middleware\\SwitchLanguageLocale, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Filament\\Http\\Middleware\\Authenticate", "telescope": "<a href=\"http://bplpnet.test/_debugbar/telescope/9f4ce6d6-9965-4d7f-b055-4fa1f92ab345\" target=\"_blank\">View in Telescope</a>", "duration": "731ms", "peak_memory": "46MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1555 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjhlNFJweU9qTWYyaXc4SEtWMDl3OGc9PSIsInZhbHVlIjoibnJRVzFVMC9nV09rS2xUUjNERDZJVG9TVlM3M3Q1RXU0S3hVMW9QRVRpMlQrc25qSm9UbFIwV1kzSzNqTUtaVU1xNExkV09NZHFLSjIyR1M3NTB1enJzbDI2bmRabmFON0VYSlN5OTFaMUdDUVVUSmMyYW15RmtjSmhtUmgyZVkzclRrTXd6eWpKbG5DUzFPY0JmaE1KQzRDVjUzQkp4Ukl4aUwwOXlRZXN6dllPL2dPbWllZE42QTRKbGFuczNMNVFENmlsaXVZb3lmcHNkZkRMejJqZDQzSHd3V3N2cU13MDJKbFJoZ0RkST0iLCJtYWMiOiI2NTRlYzEyZTY5ODY1MzRlMmY4YmM5MTM4MTdhN2Q3YWQwYzQzZTZiOGQ3YmFmZDg3YWFjMzJhMTgyYzBiMTc3IiwidGFnIjoiIn0%3D; filament_language_switch_locale=eyJpdiI6IldSalJ4dW1CcFhqWCtSR2pFSytLYUE9PSIsInZhbHVlIjoiQTVEaTRtaXhMVlhxNTBXZEUrOSt3UDVoQUJvOGdCa21ZODBwekJ5aDc5bjJ2SzgySVR1YnpMOVNWWlpSdjVWcyIsIm1hYyI6ImUyNjI0NmUyMzk3MjI2MmUyNzdiY2JhOGVhMzE0YjRjOWIyNzJiODUxMGU1NzQzYTJhY2M1YjIwYjc0ZTJkOTgiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6IkVTZUpTU1ordk4wTGVEbVNjbVpCWkE9PSIsInZhbHVlIjoiTXpwZVdIQkNLbW5ueWJpK2IxR01nZlh2c09SbDV4QXk2bG56UG56N2t2QWNvSHRheWRiTzdXSzR0Zlh1cEhWdkk3bHIzeGxEQ1BCTUw1OVltQUJ3dG84ZzhxZVo1SEx2NkxYS0E4UE4rMDdmdGpGNVVaZnJCZDhGem5UYlY5MkIiLCJtYWMiOiI4Mjc5MjA3NmJkOGJiYzIwMjI4OWMzZGMxYTYzZWMwYjRhNzdlOGE1MzE4MzM0YjM3ZjExZjZiMjNlODAwYjg0IiwidGFnIjoiIn0%3D; alfhrs_almohd_session=eyJpdiI6Im12eERtL0VNZERjRGNBZ2RHK01CMmc9PSIsInZhbHVlIjoiSlBZVzhDNmdQVXF6dnRvOWVNMlFkSndOblVKVkZYS3NTT0RSSlNRT2pnSTRuOXMvODJaVjUxSE1LMUVYV0J6R1p3TVc0U1paZzRHY1JOanNhYTNhMmZ3dkxzWFcyNW9VUlNLU3lGQ1FubldSblptYnI0WmplUHk0M3E0RnNGckQiLCJtYWMiOiI5N2M2ZGRkNjYxZGY0ODdmZGJhM2E0ODgyMzY1MjFlMjUxNTZjNDI5Yzg4YzU0MWRmYTljOWEyOGY4YmQ3MGM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">ar-EG,ar;q=0.9,en-US;q=0.8,en;q=0.7,fr;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://bplpnet.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">bplpnet.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1592335396 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"124 characters\">23|oayZmX4hCkVRzDYgX8XiyDlbdUWGhjg8WRylkmFAigbARBfuImkVfTcPvqxq|$2y$10$ndp0mY0vfh7SKC/wbKghcOK8n.7TdyC6/Z5j84PViILPym72FiJWy</span>\"\n  \"<span class=sf-dump-key>filament_language_switch_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG</span>\"\n  \"<span class=sf-dump-key>alfhrs_almohd_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OHrIdfK5jqy65dnTOAgZp53RllI0Bol0hjZv4x8G</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1592335396\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-724358972 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 03 Jul 2025 07:06:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkxPdStpL3M3U3ltTTNEMC94ZjF0ZHc9PSIsInZhbHVlIjoiSWcxNWRwN0xjZ3hIanpFZmFxY2FOdTVMWG5aVUM0LzVMY08xRVNaencyNlV4dEcvL2pDRUorcGFOZDVlM3BISTZPaVdwTFV1RDJ5dDMyaWV4QTJCN3NSTWZnYlpvVEhnbzVaOFVWWExKQkNrSTJDMlhFcmJYQjNLRFlKZlhabW8iLCJtYWMiOiIxNGIwZjhiNGE2MDQ2MmE5ZmFmY2Q1ZTkyZGYzMDI2MTA4Yjk1YmJmNDljMGYzNGFmMDYzZTYzNTEwYjY3NzgwIiwidGFnIjoiIn0%3D; expires=Thu, 03 Jul 2025 09:06:31 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">alfhrs_almohd_session=eyJpdiI6IlZCRTgva1pzOWlSUzVxRHh3bEZMZFE9PSIsInZhbHVlIjoiYVBGSE1rZndHNnV2ZS9OK2h1c0t4Z1BnQlRqdXhNV0M2RHd2WUtaR1lrL0lpditQQXAvSVF6bEdpbmI4aUhrNm1CeTkxSTU4WW05eDBxWjVlNE1MV001aDVpTmVGWU1sT1JaeU42TlcyUWJra0NtK21nRHVONnNCdjRNUGNVY28iLCJtYWMiOiI5MjUxMmYxYWYxODg2ODNjNzg0MjAwMzEwOTZmN2U3NGVmOTdjNjU3YzBlOTlhMGUzOTliNGJmMTljMjg2ZjEyIiwidGFnIjoiIn0%3D; expires=Thu, 03 Jul 2025 09:06:31 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkxPdStpL3M3U3ltTTNEMC94ZjF0ZHc9PSIsInZhbHVlIjoiSWcxNWRwN0xjZ3hIanpFZmFxY2FOdTVMWG5aVUM0LzVMY08xRVNaencyNlV4dEcvL2pDRUorcGFOZDVlM3BISTZPaVdwTFV1RDJ5dDMyaWV4QTJCN3NSTWZnYlpvVEhnbzVaOFVWWExKQkNrSTJDMlhFcmJYQjNLRFlKZlhabW8iLCJtYWMiOiIxNGIwZjhiNGE2MDQ2MmE5ZmFmY2Q1ZTkyZGYzMDI2MTA4Yjk1YmJmNDljMGYzNGFmMDYzZTYzNTEwYjY3NzgwIiwidGFnIjoiIn0%3D; expires=Thu, 03-Jul-2025 09:06:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">alfhrs_almohd_session=eyJpdiI6IlZCRTgva1pzOWlSUzVxRHh3bEZMZFE9PSIsInZhbHVlIjoiYVBGSE1rZndHNnV2ZS9OK2h1c0t4Z1BnQlRqdXhNV0M2RHd2WUtaR1lrL0lpditQQXAvSVF6bEdpbmI4aUhrNm1CeTkxSTU4WW05eDBxWjVlNE1MV001aDVpTmVGWU1sT1JaeU42TlcyUWJra0NtK21nRHVONnNCdjRNUGNVY28iLCJtYWMiOiI5MjUxMmYxYWYxODg2ODNjNzg0MjAwMzEwOTZmN2U3NGVmOTdjNjU3YzBlOTlhMGUzOTliNGJmMTljMjg2ZjEyIiwidGFnIjoiIn0%3D; expires=Thu, 03-Jul-2025 09:06:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-724358972\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1307195010 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://bplpnet.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$10$ndp0mY0vfh7SKC/wbKghcOK8n.7TdyC6/Z5j84PViILPym72FiJWy</span>\"\n  \"<span class=sf-dump-key>48040ef7f2542b39b9ba9a72983b0d88_filters</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>library</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>startDate</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>endDate</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1307195010\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://bplpnet.test/admin", "action_name": "filament.admin.pages.dashboard", "controller_action": "App\\Filament\\Pages\\Dashboard"}, "badge": null}}