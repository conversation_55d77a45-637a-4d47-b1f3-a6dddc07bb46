{"__meta": {"id": "01JZ7HQSRZ3G1CBF52NG6R3AJ5", "datetime": "2025-07-03 08:06:41", "utime": **********.823839, "method": "GET", "uri": "/admin", "ip": "127.0.0.1"}, "php": {"version": "8.4.4", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.267152, "end": **********.823885, "duration": 0.5567328929901123, "duration_str": "557ms", "measures": [{"label": "Booting", "start": **********.267152, "relative_start": 0, "end": **********.545572, "relative_end": **********.545572, "duration": 0.*****************, "duration_str": "278ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.545585, "relative_start": 0.*****************, "end": **********.823889, "relative_end": 4.0531158447265625e-06, "duration": 0.****************, "duration_str": "278ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.556318, "relative_start": 0.****************, "end": **********.558541, "relative_end": **********.558541, "duration": 0.0022230148315429688, "duration_str": "2.22ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.819932, "relative_start": 0.****************, "end": **********.820088, "relative_end": **********.820088, "duration": 0.00015592575073242188, "duration_str": "156μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.82146, "relative_start": 0.****************, "end": **********.821518, "relative_end": **********.821518, "duration": 5.793571472167969e-05, "duration_str": "58μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "route": {"uri": "GET admin", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, BezhanSalleh\\FilamentLanguageSwitch\\Http\\Middleware\\SwitchLanguageLocale, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Filament\\Http\\Middleware\\Authenticate", "uses": "App\\Filament\\Pages\\Dashboard@__invoke", "controller": "App\\Filament\\Pages\\Dashboard@render<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "filament.admin.pages.dashboard", "prefix": "admin/", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>"}, "queries": {"count": 9, "nb_statements": 8, "nb_visible_statements": 9, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.045660000000000006, "accumulated_duration_str": "45.66ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, {"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Support/Utils.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Support\\Utils.php", "line": 114}, {"index": 17, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasPanelShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasPanelShield.php", "line": 17}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 208}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 54}], "start": **********.575672, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "FilamentShield.php:121", "source": {"index": 15, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FFilamentShield.php&line=121", "ajax": false, "filename": "FilamentShield.php", "line": "121"}, "connection": "mcabplpnet", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from \"roles\" where (\"name\" = 'panel_user') limit 1", "type": "query", "params": [], "bindings": ["panel_user"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, {"index": 21, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Support/Utils.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Support\\Utils.php", "line": 114}, {"index": 22, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasPanelShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasPanelShield.php", "line": 17}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 208}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 54}], "start": **********.576339, "duration": 0.026949999999999998, "duration_str": "26.95ms", "memory": 0, "memory_str": null, "filename": "FilamentShield.php:121", "source": {"index": 20, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FFilamentShield.php&line=121", "ajax": false, "filename": "FilamentShield.php", "line": "121"}, "connection": "mcabplpnet", "explain": null, "start_percent": 0, "width_percent": 59.023}, {"sql": "select * from \"users\" where \"id\" = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.604892, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "mcabplpnet", "explain": null, "start_percent": 59.023, "width_percent": 2.387}, {"sql": "select \"name\", \"id\" from \"libraries\" where \"status\" = 'Open' and \"libraries\".\"deleted_at\" is null", "type": "query", "params": [], "bindings": ["Open"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Pages\\Dashboard.php", "line": 27}, {"index": 15, "namespace": null, "name": "vendor/filament/filament/src/Pages/Dashboard/Concerns/HasFiltersForm.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Pages\\Dashboard\\Concerns\\HasFiltersForm.php", "line": 29}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Pages/Dashboard/Concerns/HasFiltersForm.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Pages\\Dashboard\\Concerns\\HasFiltersForm.php", "line": 14}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Concerns/InteractsWithForms.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\forms\\src\\Concerns\\InteractsWithForms.php", "line": 411}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Concerns/InteractsWithForms.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\forms\\src\\Concerns\\InteractsWithForms.php", "line": 365}], "start": **********.633762, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "Dashboard.php:27", "source": {"index": 14, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Pages\\Dashboard.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FPages%2FDashboard.php&line=27", "ajax": false, "filename": "Dashboard.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 61.41, "width_percent": 2.409}, {"sql": "select \"permissions\".*, \"model_has_permissions\".\"model_id\" as \"pivot_model_id\", \"model_has_permissions\".\"permission_id\" as \"pivot_permission_id\", \"model_has_permissions\".\"model_type\" as \"pivot_model_type\" from \"permissions\" inner join \"model_has_permissions\" on \"permissions\".\"id\" = \"model_has_permissions\".\"permission_id\" where \"model_has_permissions\".\"model_id\" in (23) and \"model_has_permissions\".\"model_type\" = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": **********.69997, "duration": 0.0023599999999999997, "duration_str": "2.36ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "mcabplpnet", "explain": null, "start_percent": 63.82, "width_percent": 5.169}, {"sql": "select \"roles\".*, \"model_has_roles\".\"model_id\" as \"pivot_model_id\", \"model_has_roles\".\"role_id\" as \"pivot_role_id\", \"model_has_roles\".\"model_type\" as \"pivot_model_type\" from \"roles\" inner join \"model_has_roles\" on \"roles\".\"id\" = \"model_has_roles\".\"role_id\" where \"model_has_roles\".\"model_id\" in (23) and \"model_has_roles\".\"model_type\" = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 314}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}], "start": **********.703563, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "mcabplpnet", "explain": null, "start_percent": 68.988, "width_percent": 3}, {"sql": "select count(*) as aggregate from \"roles\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Resources/RoleResource.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Resources\\RoleResource.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.7137349, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:206", "source": {"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Resources/RoleResource.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Resources\\RoleResource.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FResources%2FRoleResource.php&line=206", "ajax": false, "filename": "RoleResource.php", "line": "206"}, "connection": "mcabplpnet", "explain": null, "start_percent": 71.989, "width_percent": 1.183}, {"sql": "select * from \"media\" where \"media\".\"model_id\" in (23) and \"media\".\"model_type\" = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 279}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 311}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 298}], "start": **********.7467551, "duration": 0.011720000000000001, "duration_str": "11.72ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:635", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=635", "ajax": false, "filename": "InteractsWithMedia.php", "line": "635"}, "connection": "mcabplpnet", "explain": null, "start_percent": 73.171, "width_percent": 25.668}, {"sql": "select count(*) as aggregate from \"roles\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Resources/RoleResource.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Resources\\RoleResource.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.772061, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:206", "source": {"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Resources/RoleResource.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Resources\\RoleResource.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FResources%2FRoleResource.php&line=206", "ajax": false, "filename": "RoleResource.php", "line": "206"}, "connection": "mcabplpnet", "explain": null, "start_percent": 98.839, "width_percent": 1.161}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 3, "is_counter": true}, "livewire": {"data": {"app.filament.pages.dashboard #wXbczvgW1XHRoNTKt3eJ": "array:4 [\n  \"data\" => array:16 [\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"areFormStateUpdateHooksDisabledForTesting\" => false\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"filters\" => array:3 [\n      \"library\" => null\n      \"startDate\" => null\n      \"endDate\" => null\n    ]\n  ]\n  \"name\" => \"app.filament.pages.dashboard\"\n  \"component\" => \"App\\Filament\\Pages\\Dashboard\"\n  \"id\" => \"wXbczvgW1XHRoNTKt3eJ\"\n]", "filament-language-switch #3bQwOMe6FNduELQIpXZU": "array:4 [\n  \"data\" => []\n  \"name\" => \"filament-language-switch\"\n  \"component\" => \"BezhanSalleh\\FilamentLanguageSwitch\\Http\\Livewire\\FilamentLanguageSwitch\"\n  \"id\" => \"3bQwOMe6FNduELQIpXZU\"\n]", "filament.livewire.notifications #JBFbvkhVwZHVU5iKCUsd": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#3211\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"JBFbvkhVwZHVU5iKCUsd\"\n]"}, "count": 3}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 52, "messages": [{"message": "[\n  ability => page_FennecBackup,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-551660445 data-indent-pad=\"  \"><span class=sf-dump-note>page_FennecBackup </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">page_FennecBackup</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-551660445\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.70697, "xdebug_link": null}, {"message": "[\n  ability => page_FennecBackup,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1027401065 data-indent-pad=\"  \"><span class=sf-dump-note>page_FennecBackup </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">page_FennecBackup</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1027401065\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.707549, "xdebug_link": null}, {"message": "[\n  ability => page_BookSearchApiPage,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1050719198 data-indent-pad=\"  \"><span class=sf-dump-note>page_BookSearchApiPage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">page_BookSearchApiPage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1050719198\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.70991, "xdebug_link": null}, {"message": "[\n  ability => page_BookSearchApiPage,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1608562339 data-indent-pad=\"  \"><span class=sf-dump-note>page_BookSearchApiPage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">page_BookSearchApiPage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1608562339\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.710271, "xdebug_link": null}, {"message": "[\n  ability => page_SystemDashboard,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2021176833 data-indent-pad=\"  \"><span class=sf-dump-note>page_SystemDashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">page_SystemDashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2021176833\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.711078, "xdebug_link": null}, {"message": "[\n  ability => page_SystemDashboard,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1322549965 data-indent-pad=\"  \"><span class=sf-dump-note>page_SystemDashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">page_SystemDashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1322549965\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.711446, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-162314809 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-162314809\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.712752, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user => 23,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-93760634 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-93760634\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.712871, "xdebug_link": null}, {"message": "[\n  ability => view_any_monitored::scheduled::task,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-41028566 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_monitored::scheduled::task </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"35 characters\">view_any_monitored::scheduled::task</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-41028566\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.715512, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTask,\n  result => true,\n  user => 23,\n  arguments => [0 => Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTask]\n]", "message_html": "<pre class=sf-dump id=sf-dump-542687530 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTask</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"52 characters\">Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTask</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"59 characters\">[0 =&gt; Spa<PERSON>\\ScheduleMonitor\\Models\\MonitoredScheduledTask]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-542687530\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.715575, "xdebug_link": null}, {"message": "[\n  ability => view_any_monitored::scheduled::task::log::item,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1944652486 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_monitored::scheduled::task::log::item </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"46 characters\">view_any_monitored::scheduled::task::log::item</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1944652486\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.71707, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\ScheduleMonitor\\Models\\MonitoredScheduledTaskLogItem,\n  result => true,\n  user => 23,\n  arguments => [0 => <PERSON><PERSON>\\ScheduleMonitor\\Models\\MonitoredScheduledTaskLogItem]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTaskLogItem</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"59 characters\">Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTaskLogItem</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"66 characters\">[0 =&gt; Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTaskLogItem]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.71721, "xdebug_link": null}, {"message": "[\n  ability => view_any_api::health::check,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>view_any_api::health::check </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">view_any_api::health::check</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.718853, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ApiHealthCheck,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\ApiHealthCheck]\n]", "message_html": "<pre class=sf-dump id=sf-dump-711532759 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ApiHealthCheck</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\ApiHealthCheck</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; App\\Models\\ApiHealthCheck]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-711532759\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.718978, "xdebug_link": null}, {"message": "[\n  ability => view_any_document::record,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-418599350 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_document::record </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">view_any_document::record</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-418599350\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.721013, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\DocumentRecord,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\DocumentRecord]\n]", "message_html": "<pre class=sf-dump id=sf-dump-565269364 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\DocumentRecord</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\DocumentRecord</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; App\\Models\\DocumentRecord]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-565269364\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.721141, "xdebug_link": null}, {"message": "[\n  ability => view_any_library,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1582470314 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_library </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_library</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1582470314\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.722227, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Library,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\Library]\n]", "message_html": "<pre class=sf-dump id=sf-dump-188037487 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Library</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Library</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Library]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-188037487\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.722345, "xdebug_link": null}, {"message": "[\n  ability => view_any_library::stats,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-18983745 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_library::stats </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view_any_library::stats</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-18983745\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.723517, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\LibraryStats,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\LibraryStats]\n]", "message_html": "<pre class=sf-dump id=sf-dump-960911262 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\LibraryStats</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\LibraryStats</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\LibraryStats]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-960911262\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.72365, "xdebug_link": null}, {"message": "[\n  ability => view_any_municipality,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1422428772 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_municipality </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">view_any_municipality</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1422428772\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.725017, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Municipality,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\Municipality]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1647046389 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Municipality</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\Municipality</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\Municipality]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1647046389\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.725141, "xdebug_link": null}, {"message": "[\n  ability => view_any_user,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-495049483 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-495049483\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.726312, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1403124618 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1403124618\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.726432, "xdebug_link": null}, {"message": "[\n  ability => view_any_wilaya,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-288207741 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_wilaya </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_wilaya</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-288207741\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.727806, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Wilaya,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\Wilaya]\n]", "message_html": "<pre class=sf-dump id=sf-dump-401847370 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Wilaya</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Wilaya</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Wilaya]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-401847370\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.727934, "xdebug_link": null}, {"message": "[\n  ability => page_FennecBackup,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1408593779 data-indent-pad=\"  \"><span class=sf-dump-note>page_FennecBackup </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">page_FennecBackup</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1408593779\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.768392, "xdebug_link": null}, {"message": "[\n  ability => page_FennecBackup,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1353571966 data-indent-pad=\"  \"><span class=sf-dump-note>page_FennecBackup </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">page_FennecBackup</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1353571966\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.768771, "xdebug_link": null}, {"message": "[\n  ability => page_BookSearchApiPage,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1196973052 data-indent-pad=\"  \"><span class=sf-dump-note>page_BookSearchApiPage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">page_BookSearchApiPage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1196973052\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.769325, "xdebug_link": null}, {"message": "[\n  ability => page_BookSearchApiPage,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-176757518 data-indent-pad=\"  \"><span class=sf-dump-note>page_BookSearchApiPage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">page_BookSearchApiPage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-176757518\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.769673, "xdebug_link": null}, {"message": "[\n  ability => page_SystemDashboard,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1268657437 data-indent-pad=\"  \"><span class=sf-dump-note>page_SystemDashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">page_SystemDashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1268657437\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.770362, "xdebug_link": null}, {"message": "[\n  ability => page_SystemDashboard,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1239106890 data-indent-pad=\"  \"><span class=sf-dump-note>page_SystemDashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">page_SystemDashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1239106890\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.770746, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1748730844 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1748730844\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.771515, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user => 23,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-377037346 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-377037346\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.771633, "xdebug_link": null}, {"message": "[\n  ability => view_any_monitored::scheduled::task,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1688165240 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_monitored::scheduled::task </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"35 characters\">view_any_monitored::scheduled::task</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1688165240\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.77373, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTask,\n  result => true,\n  user => 23,\n  arguments => [0 => Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTask]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1262606418 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTask</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"52 characters\">Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTask</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"59 characters\">[0 =&gt; Spa<PERSON>\\ScheduleMonitor\\Models\\MonitoredScheduledTask]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1262606418\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.773795, "xdebug_link": null}, {"message": "[\n  ability => view_any_monitored::scheduled::task::log::item,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-389766143 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_monitored::scheduled::task::log::item </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"46 characters\">view_any_monitored::scheduled::task::log::item</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-389766143\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.774278, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\ScheduleMonitor\\Models\\MonitoredScheduledTaskLogItem,\n  result => true,\n  user => 23,\n  arguments => [0 => <PERSON><PERSON>\\ScheduleMonitor\\Models\\MonitoredScheduledTaskLogItem]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTaskLogItem</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"59 characters\">Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTaskLogItem</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"66 characters\">[0 =&gt; Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTaskLogItem]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.774336, "xdebug_link": null}, {"message": "[\n  ability => view_any_api::health::check,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-73404279 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_api::health::check </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">view_any_api::health::check</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-73404279\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.775162, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ApiHealthCheck,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\ApiHealthCheck]\n]", "message_html": "<pre class=sf-dump id=sf-dump-587087206 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ApiHealthCheck</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\ApiHealthCheck</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; App\\Models\\ApiHealthCheck]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-587087206\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.7753, "xdebug_link": null}, {"message": "[\n  ability => view_any_document::record,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-755249598 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_document::record </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">view_any_document::record</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-755249598\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.776072, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\DocumentRecord,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\DocumentRecord]\n]", "message_html": "<pre class=sf-dump id=sf-dump-462146115 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\DocumentRecord</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\DocumentRecord</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; App\\Models\\DocumentRecord]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-462146115\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.776202, "xdebug_link": null}, {"message": "[\n  ability => view_any_library,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1494375847 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_library </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_library</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1494375847\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.776914, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Library,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\Library]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1844632436 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Library</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Library</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Library]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1844632436\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.77704, "xdebug_link": null}, {"message": "[\n  ability => view_any_library::stats,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1202834993 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_library::stats </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view_any_library::stats</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1202834993\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.777729, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\LibraryStats,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\LibraryStats]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1918479736 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\LibraryStats</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\LibraryStats</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\LibraryStats]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1918479736\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.777843, "xdebug_link": null}, {"message": "[\n  ability => view_any_municipality,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1732973669 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_municipality </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">view_any_municipality</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1732973669\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.77859, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Municipality,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\Municipality]\n]", "message_html": "<pre class=sf-dump id=sf-dump-331391617 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Municipality</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\Municipality</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\Municipality]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-331391617\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.778702, "xdebug_link": null}, {"message": "[\n  ability => view_any_user,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1091276197 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1091276197\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.779432, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1181855643 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1181855643\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.779552, "xdebug_link": null}, {"message": "[\n  ability => view_any_wilaya,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1327510967 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_wilaya </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_wilaya</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1327510967\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.780305, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Wilaya,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\Wilaya]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1021891082 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Wilaya</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Wilaya</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Wilaya]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1021891082\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.780413, "xdebug_link": null}]}, "session": {"_token": "fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG", "PHPDEBUGBAR_STACK_DATA": "[]", "_previous": "array:1 [\n  \"url\" => \"http://bplpnet.test/admin\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "23", "password_hash_web": "$2y$10$ndp0mY0vfh7SKC/wbKghcOK8n.7TdyC6/Z5j84PViILPym72FiJWy", "48040ef7f2542b39b9ba9a72983b0d88_filters": "array:3 [\n  \"library\" => null\n  \"startDate\" => null\n  \"endDate\" => null\n]"}, "request": {"data": {"status": "200 OK", "full_url": "http://bplpnet.test/admin", "action_name": "filament.admin.pages.dashboard", "controller_action": "App\\Filament\\Pages\\Dashboard", "uri": "GET admin", "controller": "App\\Filament\\Pages\\Dashboard@render<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:admin, panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, BezhanSalleh\\FilamentLanguageSwitch\\Http\\Middleware\\SwitchLanguageLocale, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Filament\\Http\\Middleware\\Authenticate", "telescope": "<a href=\"http://bplpnet.test/_debugbar/telescope/9f4ce6e6-4cdd-4f6d-8886-506e4c59a4c0\" target=\"_blank\">View in Telescope</a>", "duration": "559ms", "peak_memory": "46MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1555 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjhlNFJweU9qTWYyaXc4SEtWMDl3OGc9PSIsInZhbHVlIjoibnJRVzFVMC9nV09rS2xUUjNERDZJVG9TVlM3M3Q1RXU0S3hVMW9QRVRpMlQrc25qSm9UbFIwV1kzSzNqTUtaVU1xNExkV09NZHFLSjIyR1M3NTB1enJzbDI2bmRabmFON0VYSlN5OTFaMUdDUVVUSmMyYW15RmtjSmhtUmgyZVkzclRrTXd6eWpKbG5DUzFPY0JmaE1KQzRDVjUzQkp4Ukl4aUwwOXlRZXN6dllPL2dPbWllZE42QTRKbGFuczNMNVFENmlsaXVZb3lmcHNkZkRMejJqZDQzSHd3V3N2cU13MDJKbFJoZ0RkST0iLCJtYWMiOiI2NTRlYzEyZTY5ODY1MzRlMmY4YmM5MTM4MTdhN2Q3YWQwYzQzZTZiOGQ3YmFmZDg3YWFjMzJhMTgyYzBiMTc3IiwidGFnIjoiIn0%3D; filament_language_switch_locale=eyJpdiI6IldSalJ4dW1CcFhqWCtSR2pFSytLYUE9PSIsInZhbHVlIjoiQTVEaTRtaXhMVlhxNTBXZEUrOSt3UDVoQUJvOGdCa21ZODBwekJ5aDc5bjJ2SzgySVR1YnpMOVNWWlpSdjVWcyIsIm1hYyI6ImUyNjI0NmUyMzk3MjI2MmUyNzdiY2JhOGVhMzE0YjRjOWIyNzJiODUxMGU1NzQzYTJhY2M1YjIwYjc0ZTJkOTgiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6IlJoSExuVXR2VzFSUVhMY0VrYzU3cVE9PSIsInZhbHVlIjoiVEtoN3lFbFV4WGpNbVhmQ3BDRlZjR2ovTHNNWGRFR1RZMVZnbWpxOG9qZVFQenBNSVdhdXRHM3ltbXRWZkdMQ2U1dTRMZjAxcG5vblFRMXJnRkxic0lKOFpNejZCUXZ1L0hOc05VNjFDVTJqMXowVzJaSUQvMEI1U2Z4d2dTaVgiLCJtYWMiOiI0ODczNWNhMzkzN2Y0OWM4ODNjY2I4ODMzMjRkYTY4ZTNlYzY2ODU1OTQ4N2U4M2MwYzk4NmJjMjVhYTRkYzE3IiwidGFnIjoiIn0%3D; alfhrs_almohd_session=eyJpdiI6IjBPOVZXTlpISFEwQzh2TThsRUdzMFE9PSIsInZhbHVlIjoiUWFTbEZ5YnI5dGpzMDU0L3pZaUtpc29zNHZwTkdJSldUK0hKa1lONmM5VUhxRjUwV2QzMzZxOTVISXZnSXJMQmIwWkdjb1dsTXpoUlovWUFIYVJBS0dWSVFsWEFRWnFzR1NnTFlFK3hNRzlHVnU3bUJFWEoyd2NmNGkxaDFDajUiLCJtYWMiOiI0ZDE2ZTU5OWYzNGIwOTdlYzk2OGMxZmQ1M2UwMmZlZWE4NzM0YTY2M2RlZGU4ZDEzNjEyYzY5YzliNjY5NDZhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">ar-EG,ar;q=0.9,en-US;q=0.8,en;q=0.7,fr;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://bplpnet.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">bplpnet.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2067760474 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"124 characters\">23|oayZmX4hCkVRzDYgX8XiyDlbdUWGhjg8WRylkmFAigbARBfuImkVfTcPvqxq|$2y$10$ndp0mY0vfh7SKC/wbKghcOK8n.7TdyC6/Z5j84PViILPym72FiJWy</span>\"\n  \"<span class=sf-dump-key>filament_language_switch_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG</span>\"\n  \"<span class=sf-dump-key>alfhrs_almohd_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OHrIdfK5jqy65dnTOAgZp53RllI0Bol0hjZv4x8G</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2067760474\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-708681096 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 03 Jul 2025 07:06:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InM3dEM1akJNdkhnNFFZdnp1ZUx3eUE9PSIsInZhbHVlIjoiQ3A3M0tMVFZqL1hKNGJHd21oRXV3akpwUUtPWDJGQm5MSXVhRUNwZGY0U1Nkc045OXJyblpQK2FGSytFOEY4V0R0VENHamNzYjFiQzFMcWxFbzVUc2NadXc4T1hqdE9mQjN2aFV2TlpWd01RdnNKYlVEdElZYXBpOXFwaEVYdlciLCJtYWMiOiJkZGUyYTQ0MjU4NjNmNjA4Yzc1ZjM2NDgzY2MwNzk5MTdiMzEyNTU2ZjFmMjA0YzdmOTcxOGI3NWZlY2I0NGI5IiwidGFnIjoiIn0%3D; expires=Thu, 03 Jul 2025 09:06:41 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">alfhrs_almohd_session=eyJpdiI6InhJbHBPL280VDJTZVdFZlpOandKb0E9PSIsInZhbHVlIjoid000NEt3NmFObnFsWXAxeEQxOGtORTF0TC9lWCs2YlVDL1haSVhQS3FuOXF5ZktiNGJ3RHFoQ0VzWGtnVHRHRngrOVZrZCtnNm9peGtmSmkwS2k1OHRjUmhOc2E4Sk1UMWNYTmNFWU04aGgrRytJeWRaZGRxbEh0cTVrdlJNSTciLCJtYWMiOiIwZjI2MTJhODVhYmMxMGQxN2MxZWJiNGUyZGQ2NjFlN2UwNzhkMGEyMTExYWY5NzNiZTM2ZjBmNGRkZDE3NzdmIiwidGFnIjoiIn0%3D; expires=Thu, 03 Jul 2025 09:06:41 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InM3dEM1akJNdkhnNFFZdnp1ZUx3eUE9PSIsInZhbHVlIjoiQ3A3M0tMVFZqL1hKNGJHd21oRXV3akpwUUtPWDJGQm5MSXVhRUNwZGY0U1Nkc045OXJyblpQK2FGSytFOEY4V0R0VENHamNzYjFiQzFMcWxFbzVUc2NadXc4T1hqdE9mQjN2aFV2TlpWd01RdnNKYlVEdElZYXBpOXFwaEVYdlciLCJtYWMiOiJkZGUyYTQ0MjU4NjNmNjA4Yzc1ZjM2NDgzY2MwNzk5MTdiMzEyNTU2ZjFmMjA0YzdmOTcxOGI3NWZlY2I0NGI5IiwidGFnIjoiIn0%3D; expires=Thu, 03-Jul-2025 09:06:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">alfhrs_almohd_session=eyJpdiI6InhJbHBPL280VDJTZVdFZlpOandKb0E9PSIsInZhbHVlIjoid000NEt3NmFObnFsWXAxeEQxOGtORTF0TC9lWCs2YlVDL1haSVhQS3FuOXF5ZktiNGJ3RHFoQ0VzWGtnVHRHRngrOVZrZCtnNm9peGtmSmkwS2k1OHRjUmhOc2E4Sk1UMWNYTmNFWU04aGgrRytJeWRaZGRxbEh0cTVrdlJNSTciLCJtYWMiOiIwZjI2MTJhODVhYmMxMGQxN2MxZWJiNGUyZGQ2NjFlN2UwNzhkMGEyMTExYWY5NzNiZTM2ZjBmNGRkZDE3NzdmIiwidGFnIjoiIn0%3D; expires=Thu, 03-Jul-2025 09:06:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-708681096\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1925281666 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://bplpnet.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$10$ndp0mY0vfh7SKC/wbKghcOK8n.7TdyC6/Z5j84PViILPym72FiJWy</span>\"\n  \"<span class=sf-dump-key>48040ef7f2542b39b9ba9a72983b0d88_filters</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>library</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>startDate</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>endDate</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1925281666\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://bplpnet.test/admin", "action_name": "filament.admin.pages.dashboard", "controller_action": "App\\Filament\\Pages\\Dashboard"}, "badge": null}}