{"__meta": {"id": "01JZ7GQBJF5CTK9C5VF5ENMQVG", "datetime": "2025-07-03 07:48:58", "utime": **********.704435, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.4.4", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.198645, "end": **********.704467, "duration": 0.505821943283081, "duration_str": "506ms", "measures": [{"label": "Booting", "start": **********.198645, "relative_start": 0, "end": **********.477366, "relative_end": **********.477366, "duration": 0.****************, "duration_str": "279ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.477376, "relative_start": 0.****************, "end": **********.704469, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "227ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.487693, "relative_start": 0.****************, "end": **********.489758, "relative_end": **********.489758, "duration": 0.002064943313598633, "duration_str": "2.06ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.701554, "relative_start": 0.****************, "end": **********.702358, "relative_end": **********.702358, "duration": 0.0008039474487304688, "duration_str": "804μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "40MB"}, "exceptions": {"count": 0, "exceptions": []}, "route": {"uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>"}, "queries": {"count": 86, "nb_statements": 85, "nb_visible_statements": 86, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.07176999999999997, "accumulated_duration_str": "71.77ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, {"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Support/Utils.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Support\\Utils.php", "line": 114}, {"index": 17, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasPanelShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasPanelShield.php", "line": 17}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 208}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 54}], "start": **********.508865, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "FilamentShield.php:121", "source": {"index": 15, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FFilamentShield.php&line=121", "ajax": false, "filename": "FilamentShield.php", "line": "121"}, "connection": "mcabplpnet", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from \"roles\" where (\"name\" = 'panel_user') limit 1", "type": "query", "params": [], "bindings": ["panel_user"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, {"index": 21, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Support/Utils.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Support\\Utils.php", "line": 114}, {"index": 22, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasPanelShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasPanelShield.php", "line": 17}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 208}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 54}], "start": **********.509489, "duration": 0.03065, "duration_str": "30.65ms", "memory": 0, "memory_str": null, "filename": "FilamentShield.php:121", "source": {"index": 20, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FFilamentShield.php&line=121", "ajax": false, "filename": "FilamentShield.php", "line": "121"}, "connection": "mcabplpnet", "explain": null, "start_percent": 0, "width_percent": 42.706}, {"sql": "select * from \"users\" where \"id\" = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.541086, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "mcabplpnet", "explain": null, "start_percent": 42.706, "width_percent": 1.7}, {"sql": "select * from \"libraries\" where \"status\" = 'Open' and \"libraries\".\"deleted_at\" is null", "type": "query", "params": [], "bindings": ["Open"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 18}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.554261, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:18", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=18", "ajax": false, "filename": "ApiHealthStatus.php", "line": "18"}, "connection": "mcabplpnet", "explain": null, "start_percent": 44.406, "width_percent": 1.588}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 1 group by \"endpoint\"", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.55725, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 45.994, "width_percent": 1.519}, {"sql": "select * from \"api_health_checks\" where \"id\" in (2, 3, 1)", "type": "query", "params": [], "bindings": [2, 3, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.559257, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 47.513, "width_percent": 0.502}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 2 group by \"endpoint\"", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.560712, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 48.014, "width_percent": 0.418}, {"sql": "select * from \"api_health_checks\" where \"id\" in (5, 6, 4)", "type": "query", "params": [], "bindings": [5, 6, 4], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.56194, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 48.432, "width_percent": 0.557}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 3 group by \"endpoint\"", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.5636182, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 48.99, "width_percent": 0.627}, {"sql": "select * from \"api_health_checks\" where \"id\" in (8, 9, 7)", "type": "query", "params": [], "bindings": [8, 9, 7], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.564914, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 49.617, "width_percent": 0.557}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 4 group by \"endpoint\"", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.5662591, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 50.174, "width_percent": 0.641}, {"sql": "select * from \"api_health_checks\" where \"id\" in (11, 12, 10)", "type": "query", "params": [], "bindings": [11, 12, 10], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.5676372, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 50.815, "width_percent": 0.641}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 5 group by \"endpoint\"", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.569566, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 51.456, "width_percent": 0.738}, {"sql": "select * from \"api_health_checks\" where \"id\" in (14, 15, 13)", "type": "query", "params": [], "bindings": [14, 15, 13], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.57103, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 52.195, "width_percent": 0.738}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 6 group by \"endpoint\"", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.572572, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 52.933, "width_percent": 0.502}, {"sql": "select * from \"api_health_checks\" where \"id\" in (17, 18, 16)", "type": "query", "params": [], "bindings": [17, 18, 16], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.573805, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 53.435, "width_percent": 0.627}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 7 group by \"endpoint\"", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.575308, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 54.062, "width_percent": 0.669}, {"sql": "select * from \"api_health_checks\" where \"id\" in (20, 21, 19)", "type": "query", "params": [], "bindings": [20, 21, 19], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.5771568, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 54.73, "width_percent": 0.85}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 8 group by \"endpoint\"", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.579339, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 55.58, "width_percent": 0.878}, {"sql": "select * from \"api_health_checks\" where \"id\" in (23, 24, 22)", "type": "query", "params": [], "bindings": [23, 24, 22], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.5815828, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 56.458, "width_percent": 0.85}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 9 group by \"endpoint\"", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.584013, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 57.308, "width_percent": 1.059}, {"sql": "select * from \"api_health_checks\" where \"id\" in (26, 27, 25)", "type": "query", "params": [], "bindings": [26, 27, 25], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.5859962, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 58.367, "width_percent": 0.697}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 10 group by \"endpoint\"", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.5877738, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 59.064, "width_percent": 0.836}, {"sql": "select * from \"api_health_checks\" where \"id\" in (29, 30, 28)", "type": "query", "params": [], "bindings": [29, 30, 28], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.589387, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 59.9, "width_percent": 0.683}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 11 group by \"endpoint\"", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.591152, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 60.582, "width_percent": 0.711}, {"sql": "select * from \"api_health_checks\" where \"id\" in (32, 33, 31)", "type": "query", "params": [], "bindings": [32, 33, 31], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.593658, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 61.293, "width_percent": 0.697}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 12 group by \"endpoint\"", "type": "query", "params": [], "bindings": [12], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.595368, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 61.99, "width_percent": 0.725}, {"sql": "select * from \"api_health_checks\" where \"id\" in (35, 36, 34)", "type": "query", "params": [], "bindings": [35, 36, 34], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.597339, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 62.714, "width_percent": 0.766}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 13 group by \"endpoint\"", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.599161, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 63.481, "width_percent": 0.85}, {"sql": "select * from \"api_health_checks\" where \"id\" in (38, 39, 37)", "type": "query", "params": [], "bindings": [38, 39, 37], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.601235, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 64.331, "width_percent": 0.794}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 14 group by \"endpoint\"", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6041682, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 65.125, "width_percent": 0.906}, {"sql": "select * from \"api_health_checks\" where \"id\" in (41, 42, 40)", "type": "query", "params": [], "bindings": [41, 42, 40], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6062908, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 66.03, "width_percent": 1.045}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 15 group by \"endpoint\"", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.60956, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 67.075, "width_percent": 1.198}, {"sql": "select * from \"api_health_checks\" where \"id\" in (44, 45, 43)", "type": "query", "params": [], "bindings": [44, 45, 43], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.611945, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 68.274, "width_percent": 0.822}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 17 group by \"endpoint\"", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.61377, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 69.096, "width_percent": 0.599}, {"sql": "select * from \"api_health_checks\" where \"id\" in (47, 48, 46)", "type": "query", "params": [], "bindings": [47, 48, 46], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6152022, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 69.695, "width_percent": 0.627}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 18 group by \"endpoint\"", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.617783, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 70.322, "width_percent": 0.697}, {"sql": "select * from \"api_health_checks\" where \"id\" in (50, 49)", "type": "query", "params": [], "bindings": [50, 49], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6192791, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 71.019, "width_percent": 0.683}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 19 group by \"endpoint\"", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.621659, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 71.701, "width_percent": 0.794}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.623119, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 72.495, "width_percent": 0.641}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 20 group by \"endpoint\"", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.624505, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 73.136, "width_percent": 0.446}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6258152, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 73.582, "width_percent": 0.446}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 21 group by \"endpoint\"", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6270099, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 74.028, "width_percent": 0.516}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6282182, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 74.544, "width_percent": 0.432}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 23 group by \"endpoint\"", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.62939, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 74.976, "width_percent": 0.502}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6306221, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 75.477, "width_percent": 0.446}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 24 group by \"endpoint\"", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.632017, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 75.923, "width_percent": 0.738}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6338918, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 76.662, "width_percent": 0.808}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 25 group by \"endpoint\"", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6357388, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 77.47, "width_percent": 0.836}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6384032, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 78.306, "width_percent": 0.878}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 26 group by \"endpoint\"", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.64085, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 79.184, "width_percent": 0.892}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.642527, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 80.075, "width_percent": 0.711}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 27 group by \"endpoint\"", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.644019, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 80.786, "width_percent": 0.808}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.645536, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 81.594, "width_percent": 0.516}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 28 group by \"endpoint\"", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.646971, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 82.11, "width_percent": 0.752}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6490319, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 82.862, "width_percent": 0.766}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 29 group by \"endpoint\"", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.651642, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 83.628, "width_percent": 0.906}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6540132, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 84.534, "width_percent": 0.641}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 30 group by \"endpoint\"", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.655458, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 85.175, "width_percent": 0.655}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.657164, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 85.83, "width_percent": 0.655}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 31 group by \"endpoint\"", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6585631, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 86.485, "width_percent": 0.627}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.660043, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 87.112, "width_percent": 0.529}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 32 group by \"endpoint\"", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6616712, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 87.641, "width_percent": 0.669}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6633859, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 88.31, "width_percent": 0.571}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 33 group by \"endpoint\"", "type": "query", "params": [], "bindings": [33], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.664802, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 88.881, "width_percent": 0.627}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6670702, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 89.508, "width_percent": 0.627}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 34 group by \"endpoint\"", "type": "query", "params": [], "bindings": [34], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.668405, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 90.135, "width_percent": 0.641}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.669698, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 90.776, "width_percent": 0.599}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 35 group by \"endpoint\"", "type": "query", "params": [], "bindings": [35], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.671334, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 91.375, "width_percent": 0.557}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6725209, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 91.933, "width_percent": 0.348}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 36 group by \"endpoint\"", "type": "query", "params": [], "bindings": [36], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.674258, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 92.281, "width_percent": 0.766}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6757169, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 93.047, "width_percent": 0.599}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 37 group by \"endpoint\"", "type": "query", "params": [], "bindings": [37], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.677061, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 93.646, "width_percent": 0.516}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6785781, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 94.162, "width_percent": 0.446}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 38 group by \"endpoint\"", "type": "query", "params": [], "bindings": [38], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.679715, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 94.608, "width_percent": 0.46}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6808288, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 95.068, "width_percent": 0.348}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 39 group by \"endpoint\"", "type": "query", "params": [], "bindings": [39], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6818888, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 95.416, "width_percent": 0.362}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6829991, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 95.778, "width_percent": 0.418}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 40 group by \"endpoint\"", "type": "query", "params": [], "bindings": [40], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.684188, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 96.196, "width_percent": 0.571}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.68551, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 96.767, "width_percent": 0.474}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 43 group by \"endpoint\"", "type": "query", "params": [], "bindings": [43], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6866522, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 97.241, "width_percent": 0.418}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.687731, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 97.659, "width_percent": 0.446}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 44 group by \"endpoint\"", "type": "query", "params": [], "bindings": [44], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.688874, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 98.105, "width_percent": 0.404}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.689987, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 98.509, "width_percent": 0.362}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 45 group by \"endpoint\"", "type": "query", "params": [], "bindings": [45], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.691118, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:27", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=27", "ajax": false, "filename": "ApiHealthStatus.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 98.871, "width_percent": 0.655}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.692401, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:30", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=30", "ajax": false, "filename": "ApiHealthStatus.php", "line": "30"}, "connection": "mcabplpnet", "explain": null, "start_percent": 99.526, "width_percent": 0.474}]}, "models": {"data": {"App\\Models\\ApiHealthCheck": {"value": 100, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FModels%2FApiHealthCheck.php&line=1", "ajax": false, "filename": "ApiHealthCheck.php", "line": "?"}}, "App\\Models\\Library": {"value": 41, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FModels%2FLibrary.php&line=1", "ajax": false, "filename": "Library.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 143, "is_counter": true}, "livewire": {"data": {"app.filament.widgets.api-health-status #ctLA9FLARExqqVJCkkY4": "array:4 [\n  \"data\" => []\n  \"name\" => \"app.filament.widgets.api-health-status\"\n  \"component\" => \"App\\Filament\\Widgets\\ApiHealthStatus\"\n  \"id\" => \"ctLA9FLARExqqVJCkkY4\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG", "PHPDEBUGBAR_STACK_DATA": "[]", "_previous": "array:1 [\n  \"url\" => \"http://bplpnet.test/admin\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "23", "password_hash_web": "$2y$10$ndp0mY0vfh7SKC/wbKghcOK8n.7TdyC6/Z5j84PViILPym72FiJWy", "48040ef7f2542b39b9ba9a72983b0d88_filters": "array:3 [\n  \"library\" => null\n  \"startDate\" => null\n  \"endDate\" => null\n]"}, "request": {"data": {"status": "200 OK", "full_url": "http://bplpnet.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "telescope": "<a href=\"http://bplpnet.test/_debugbar/telescope/9f4ce090-1ee0-406d-b1d9-87221a8ff8d3\" target=\"_blank\">View in Telescope</a>", "duration": "515ms", "peak_memory": "42MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-873820360 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-873820360\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1627304946 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"308 characters\">{&quot;data&quot;:[],&quot;memo&quot;:{&quot;id&quot;:&quot;ctLA9FLARExqqVJCkkY4&quot;,&quot;name&quot;:&quot;app.filament.widgets.api-health-status&quot;,&quot;path&quot;:&quot;admin&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;lazyLoaded&quot;:false,&quot;lazyIsolated&quot;:true,&quot;errors&quot;:[],&quot;locale&quot;:&quot;ar&quot;},&quot;checksum&quot;:&quot;5a420075d3894ff5b13d8bd6271cdce6532b9eb14773c2708812e9ae2b8cca02&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"10 characters\">__lazyLoad</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"344 characters\">eyJkYXRhIjp7ImZvck1vdW50IjpbeyJmaWx0ZXJzIjpbeyJsaWJyYXJ5IjpudWxsLCJzdGFydERhdGUiOm51bGwsImVuZERhdGUiOm51bGx9LHsicyI6ImFyciJ9XX0seyJzIjoiYXJyIn1dfSwibWVtbyI6eyJpZCI6IkFxVU5wVE5QRWJYTENiS2pzSzhlIiwibmFtZSI6Il9fbW91bnRQYXJhbXNDb250YWluZXIifSwiY2hlY2tzdW0iOiI5ZmYxNThlYmY2ODY3Yzg0OWQ3YTkxOTZlNGMwYmI2NTRkZmUyYzJhMzI4YWUwMGExYzVmZDQ5NDA3YmFlYmQwIn0=</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1627304946\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-402410147 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1555 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjhlNFJweU9qTWYyaXc4SEtWMDl3OGc9PSIsInZhbHVlIjoibnJRVzFVMC9nV09rS2xUUjNERDZJVG9TVlM3M3Q1RXU0S3hVMW9QRVRpMlQrc25qSm9UbFIwV1kzSzNqTUtaVU1xNExkV09NZHFLSjIyR1M3NTB1enJzbDI2bmRabmFON0VYSlN5OTFaMUdDUVVUSmMyYW15RmtjSmhtUmgyZVkzclRrTXd6eWpKbG5DUzFPY0JmaE1KQzRDVjUzQkp4Ukl4aUwwOXlRZXN6dllPL2dPbWllZE42QTRKbGFuczNMNVFENmlsaXVZb3lmcHNkZkRMejJqZDQzSHd3V3N2cU13MDJKbFJoZ0RkST0iLCJtYWMiOiI2NTRlYzEyZTY5ODY1MzRlMmY4YmM5MTM4MTdhN2Q3YWQwYzQzZTZiOGQ3YmFmZDg3YWFjMzJhMTgyYzBiMTc3IiwidGFnIjoiIn0%3D; filament_language_switch_locale=eyJpdiI6IldSalJ4dW1CcFhqWCtSR2pFSytLYUE9PSIsInZhbHVlIjoiQTVEaTRtaXhMVlhxNTBXZEUrOSt3UDVoQUJvOGdCa21ZODBwekJ5aDc5bjJ2SzgySVR1YnpMOVNWWlpSdjVWcyIsIm1hYyI6ImUyNjI0NmUyMzk3MjI2MmUyNzdiY2JhOGVhMzE0YjRjOWIyNzJiODUxMGU1NzQzYTJhY2M1YjIwYjc0ZTJkOTgiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6Im5RbDFFa2JxYWdrdVBiMlppRUhHa0E9PSIsInZhbHVlIjoiNFdqems2aFNMdWJlbFdHd3pvQXRPZVUvSXMzQWVzR2RVV3RTMmhMSWZLQ3ArMGVWUXZKZnlHRStMWDc5bXh2dkZwS2lUUEJ3Q2NyZjB2OWZMMG5xSTVhTzNvTklpMWV5SXdsUnV5RWVPMHhLMUwwTXhYcEdVcWRaT244eXVRN2YiLCJtYWMiOiJiNDRlNzAyYTczZWYxNTA5Y2I4NmJjN2M1ZWIyN2I0MmIzMTVkOTQ2MTc2ZDc3ZjdkODhmZGY4ODI2YzE3YjNkIiwidGFnIjoiIn0%3D; alfhrs_almohd_session=eyJpdiI6Im5uK3Bwam80SCtEcXNpRno5K0RQVHc9PSIsInZhbHVlIjoib0lkNmVLVktUNFFGa2hmbXd0VDJSVHkvVE1FTkwwNVprMTgzQjZ5eGRheEVOa3dlZU5IMjZlcTVwUjlTbWE1N00yemg1RFFoQ0Jsd2M5cmF5amx4S1RLRHNHSGJEL3ZvTmZESzhLMCtwdUlVV2NOZU9CVlRCTEtVSVZBVzFGZW4iLCJtYWMiOiJmMDFhYjJkMWFhMzg0ZjQ3ODYzMzQ5NTE5ZWYzN2I0ZGVmMzg2NGRiZGFiMWQ4MDgzZjU3MGExNTNjNDM5ZGMzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">ar-EG,ar;q=0.9,en-US;q=0.8,en;q=0.7,fr;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://bplpnet.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">http://bplpnet.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">847</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">bplpnet.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-402410147\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-433546209 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"124 characters\">23|oayZmX4hCkVRzDYgX8XiyDlbdUWGhjg8WRylkmFAigbARBfuImkVfTcPvqxq|$2y$10$ndp0mY0vfh7SKC/wbKghcOK8n.7TdyC6/Z5j84PViILPym72FiJWy</span>\"\n  \"<span class=sf-dump-key>filament_language_switch_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG</span>\"\n  \"<span class=sf-dump-key>alfhrs_almohd_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OHrIdfK5jqy65dnTOAgZp53RllI0Bol0hjZv4x8G</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-433546209\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1555676758 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 03 Jul 2025 06:48:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1555676758\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1776794635 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://bplpnet.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$10$ndp0mY0vfh7SKC/wbKghcOK8n.7TdyC6/Z5j84PViILPym72FiJWy</span>\"\n  \"<span class=sf-dump-key>48040ef7f2542b39b9ba9a72983b0d88_filters</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>library</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>startDate</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>endDate</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1776794635\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://bplpnet.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}