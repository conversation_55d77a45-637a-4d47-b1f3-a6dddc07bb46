{"__meta": {"id": "01JZ7GWQJMZ506XG3WAQ8F2CQ5", "datetime": "2025-07-03 07:51:54", "utime": **********.837296, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.4.4", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.35639, "end": **********.837327, "duration": 0.48093700408935547, "duration_str": "481ms", "measures": [{"label": "Booting", "start": **********.35639, "relative_start": 0, "end": **********.622727, "relative_end": **********.622727, "duration": 0.*****************, "duration_str": "266ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.622735, "relative_start": 0.****************, "end": **********.837329, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "215ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.633745, "relative_start": 0.****************, "end": **********.635905, "relative_end": **********.635905, "duration": 0.0021600723266601562, "duration_str": "2.16ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.834606, "relative_start": 0.*****************, "end": **********.835423, "relative_end": **********.835423, "duration": 0.0008170604705810547, "duration_str": "817μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "40MB"}, "exceptions": {"count": 0, "exceptions": []}, "route": {"uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>"}, "queries": {"count": 86, "nb_statements": 85, "nb_visible_statements": 86, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.06347000000000003, "accumulated_duration_str": "63.47ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, {"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Support/Utils.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Support\\Utils.php", "line": 114}, {"index": 17, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasPanelShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasPanelShield.php", "line": 17}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 208}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 54}], "start": **********.654137, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "FilamentShield.php:121", "source": {"index": 15, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FFilamentShield.php&line=121", "ajax": false, "filename": "FilamentShield.php", "line": "121"}, "connection": "mcabplpnet", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from \"roles\" where (\"name\" = 'panel_user') limit 1", "type": "query", "params": [], "bindings": ["panel_user"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, {"index": 21, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Support/Utils.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Support\\Utils.php", "line": 114}, {"index": 22, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasPanelShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasPanelShield.php", "line": 17}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 208}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 54}], "start": **********.6545968, "duration": 0.02693, "duration_str": "26.93ms", "memory": 0, "memory_str": null, "filename": "FilamentShield.php:121", "source": {"index": 20, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FFilamentShield.php&line=121", "ajax": false, "filename": "FilamentShield.php", "line": "121"}, "connection": "mcabplpnet", "explain": null, "start_percent": 0, "width_percent": 42.429}, {"sql": "select * from \"users\" where \"id\" = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.6824818, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "mcabplpnet", "explain": null, "start_percent": 42.429, "width_percent": 1.796}, {"sql": "select * from \"libraries\" where \"status\" = 'Open' and \"libraries\".\"deleted_at\" is null", "type": "query", "params": [], "bindings": ["Open"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 19}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.7002852, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:19", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=19", "ajax": false, "filename": "ApiHealthStatus.php", "line": "19"}, "connection": "mcabplpnet", "explain": null, "start_percent": 44.226, "width_percent": 1.843}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 1 group by \"endpoint\"", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.70295, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 46.069, "width_percent": 1.686}, {"sql": "select * from \"api_health_checks\" where \"id\" in (2, 3, 1)", "type": "query", "params": [], "bindings": [2, 3, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.705028, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 47.755, "width_percent": 0.788}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 2 group by \"endpoint\"", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.70783, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 48.543, "width_percent": 0.599}, {"sql": "select * from \"api_health_checks\" where \"id\" in (5, 6, 4)", "type": "query", "params": [], "bindings": [5, 6, 4], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.7093048, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 49.141, "width_percent": 0.646}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 3 group by \"endpoint\"", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.711206, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 49.787, "width_percent": 0.945}, {"sql": "select * from \"api_health_checks\" where \"id\" in (8, 9, 7)", "type": "query", "params": [], "bindings": [8, 9, 7], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.712708, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 50.733, "width_percent": 0.504}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 4 group by \"endpoint\"", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.714199, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 51.237, "width_percent": 0.583}, {"sql": "select * from \"api_health_checks\" where \"id\" in (11, 12, 10)", "type": "query", "params": [], "bindings": [11, 12, 10], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.715729, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 51.82, "width_percent": 0.725}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 5 group by \"endpoint\"", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.718333, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 52.545, "width_percent": 0.63}, {"sql": "select * from \"api_health_checks\" where \"id\" in (14, 15, 13)", "type": "query", "params": [], "bindings": [14, 15, 13], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.719582, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 53.175, "width_percent": 0.504}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 6 group by \"endpoint\"", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.720789, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 53.679, "width_percent": 0.504}, {"sql": "select * from \"api_health_checks\" where \"id\" in (17, 18, 16)", "type": "query", "params": [], "bindings": [17, 18, 16], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.721914, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 54.183, "width_percent": 0.425}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 7 group by \"endpoint\"", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.723168, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 54.608, "width_percent": 0.677}, {"sql": "select * from \"api_health_checks\" where \"id\" in (20, 21, 19)", "type": "query", "params": [], "bindings": [20, 21, 19], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.724554, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 55.286, "width_percent": 0.536}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 8 group by \"endpoint\"", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.726079, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 55.822, "width_percent": 0.677}, {"sql": "select * from \"api_health_checks\" where \"id\" in (23, 24, 22)", "type": "query", "params": [], "bindings": [23, 24, 22], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.727355, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 56.499, "width_percent": 0.52}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 9 group by \"endpoint\"", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.728621, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 57.019, "width_percent": 0.457}, {"sql": "select * from \"api_health_checks\" where \"id\" in (26, 27, 25)", "type": "query", "params": [], "bindings": [26, 27, 25], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.729712, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 57.476, "width_percent": 0.441}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 10 group by \"endpoint\"", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.730893, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 57.917, "width_percent": 0.536}, {"sql": "select * from \"api_health_checks\" where \"id\" in (29, 30, 28)", "type": "query", "params": [], "bindings": [29, 30, 28], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.732026, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 58.453, "width_percent": 0.536}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 11 group by \"endpoint\"", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.733342, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 58.988, "width_percent": 0.614}, {"sql": "select * from \"api_health_checks\" where \"id\" in (32, 33, 31)", "type": "query", "params": [], "bindings": [32, 33, 31], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.7346432, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 59.603, "width_percent": 0.425}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 12 group by \"endpoint\"", "type": "query", "params": [], "bindings": [12], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.735835, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 60.028, "width_percent": 0.599}, {"sql": "select * from \"api_health_checks\" where \"id\" in (35, 36, 34)", "type": "query", "params": [], "bindings": [35, 36, 34], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.737186, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 60.627, "width_percent": 0.583}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 13 group by \"endpoint\"", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.73848, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 61.21, "width_percent": 0.536}, {"sql": "select * from \"api_health_checks\" where \"id\" in (38, 39, 37)", "type": "query", "params": [], "bindings": [38, 39, 37], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.739767, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 61.746, "width_percent": 0.473}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 14 group by \"endpoint\"", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.7416382, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 62.218, "width_percent": 0.993}, {"sql": "select * from \"api_health_checks\" where \"id\" in (41, 42, 40)", "type": "query", "params": [], "bindings": [41, 42, 40], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.743272, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 63.211, "width_percent": 0.788}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 15 group by \"endpoint\"", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.744846, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 63.999, "width_percent": 0.756}, {"sql": "select * from \"api_health_checks\" where \"id\" in (44, 45, 43)", "type": "query", "params": [], "bindings": [44, 45, 43], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.746258, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 64.755, "width_percent": 0.662}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 17 group by \"endpoint\"", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.747901, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 65.417, "width_percent": 0.756}, {"sql": "select * from \"api_health_checks\" where \"id\" in (47, 48, 46)", "type": "query", "params": [], "bindings": [47, 48, 46], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.749825, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 66.173, "width_percent": 0.93}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 18 group by \"endpoint\"", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.751594, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 67.103, "width_percent": 0.693}, {"sql": "select * from \"api_health_checks\" where \"id\" in (50, 49)", "type": "query", "params": [], "bindings": [50, 49], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.753489, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 67.796, "width_percent": 0.473}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 19 group by \"endpoint\"", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.754722, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 68.268, "width_percent": 0.788}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.756274, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 69.056, "width_percent": 0.725}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 20 group by \"endpoint\"", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.757777, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 69.781, "width_percent": 0.725}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.759228, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 70.506, "width_percent": 0.583}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 21 group by \"endpoint\"", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.760572, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 71.089, "width_percent": 0.646}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.761879, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 71.735, "width_percent": 0.614}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 23 group by \"endpoint\"", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.763268, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 72.349, "width_percent": 0.945}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.76486, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 73.294, "width_percent": 0.662}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 24 group by \"endpoint\"", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.7662451, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 73.956, "width_percent": 0.693}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.767544, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 74.649, "width_percent": 0.567}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 25 group by \"endpoint\"", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.76885, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 75.217, "width_percent": 0.725}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.770268, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 75.941, "width_percent": 0.646}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 26 group by \"endpoint\"", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.7718349, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 76.587, "width_percent": 0.867}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.773769, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 77.454, "width_percent": 0.851}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 27 group by \"endpoint\"", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.7756789, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 78.305, "width_percent": 0.867}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.777879, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 79.171, "width_percent": 0.961}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 28 group by \"endpoint\"", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.780587, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 80.132, "width_percent": 1.197}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.7832859, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 81.33, "width_percent": 0.898}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 29 group by \"endpoint\"", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.785898, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 82.228, "width_percent": 0.945}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.7876952, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 83.173, "width_percent": 0.741}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 30 group by \"endpoint\"", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.790139, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 83.914, "width_percent": 1.119}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.792347, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 85.032, "width_percent": 0.551}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 31 group by \"endpoint\"", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.7947328, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 85.584, "width_percent": 0.677}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.7961109, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 86.261, "width_percent": 0.614}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 32 group by \"endpoint\"", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.797393, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 86.876, "width_percent": 0.551}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.798538, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 87.427, "width_percent": 0.394}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 33 group by \"endpoint\"", "type": "query", "params": [], "bindings": [33], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.799663, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 87.821, "width_percent": 0.473}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.800699, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 88.294, "width_percent": 0.425}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 34 group by \"endpoint\"", "type": "query", "params": [], "bindings": [34], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.801829, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 88.719, "width_percent": 0.772}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.803243, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 89.491, "width_percent": 0.599}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 35 group by \"endpoint\"", "type": "query", "params": [], "bindings": [35], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.805555, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 90.09, "width_percent": 1.071}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.807314, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 91.161, "width_percent": 0.567}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 36 group by \"endpoint\"", "type": "query", "params": [], "bindings": [36], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.808543, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 91.728, "width_percent": 0.567}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.8097532, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 92.296, "width_percent": 0.473}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 37 group by \"endpoint\"", "type": "query", "params": [], "bindings": [37], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.8109121, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 92.768, "width_percent": 0.473}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.812023, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 93.241, "width_percent": 0.599}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 38 group by \"endpoint\"", "type": "query", "params": [], "bindings": [38], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.813241, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 93.84, "width_percent": 0.536}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.814358, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 94.375, "width_percent": 0.473}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 39 group by \"endpoint\"", "type": "query", "params": [], "bindings": [39], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.8156629, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 94.848, "width_percent": 0.583}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.816904, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 95.431, "width_percent": 0.567}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 40 group by \"endpoint\"", "type": "query", "params": [], "bindings": [40], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.8181958, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 95.998, "width_percent": 0.677}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.819569, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 96.676, "width_percent": 0.536}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 43 group by \"endpoint\"", "type": "query", "params": [], "bindings": [43], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.821172, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 97.211, "width_percent": 0.662}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.82268, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 97.873, "width_percent": 0.599}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 44 group by \"endpoint\"", "type": "query", "params": [], "bindings": [44], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.823913, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 98.472, "width_percent": 0.441}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.8249128, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 98.913, "width_percent": 0.41}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 45 group by \"endpoint\"", "type": "query", "params": [], "bindings": [45], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.8259199, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 99.323, "width_percent": 0.362}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.826835, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 99.685, "width_percent": 0.315}]}, "models": {"data": {"App\\Models\\ApiHealthCheck": {"value": 100, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FModels%2FApiHealthCheck.php&line=1", "ajax": false, "filename": "ApiHealthCheck.php", "line": "?"}}, "App\\Models\\Library": {"value": 41, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FModels%2FLibrary.php&line=1", "ajax": false, "filename": "Library.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 143, "is_counter": true}, "livewire": {"data": {"app.filament.widgets.api-health-status #s5ziAnG5rk0gfBsWxA7Y": "array:4 [\n  \"data\" => []\n  \"name\" => \"app.filament.widgets.api-health-status\"\n  \"component\" => \"App\\Filament\\Widgets\\ApiHealthStatus\"\n  \"id\" => \"s5ziAnG5rk0gfBsWxA7Y\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG", "PHPDEBUGBAR_STACK_DATA": "[]", "_previous": "array:1 [\n  \"url\" => \"http://bplpnet.test/admin\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "23", "password_hash_web": "$2y$10$ndp0mY0vfh7SKC/wbKghcOK8n.7TdyC6/Z5j84PViILPym72FiJWy", "48040ef7f2542b39b9ba9a72983b0d88_filters": "array:3 [\n  \"library\" => null\n  \"startDate\" => null\n  \"endDate\" => null\n]"}, "request": {"data": {"status": "200 OK", "full_url": "http://bplpnet.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "telescope": "<a href=\"http://bplpnet.test/_debugbar/telescope/9f4ce19c-e049-467e-badc-e3664a57d06d\" target=\"_blank\">View in Telescope</a>", "duration": "490ms", "peak_memory": "44MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1361809862 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1361809862\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-704349270 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"308 characters\">{&quot;data&quot;:[],&quot;memo&quot;:{&quot;id&quot;:&quot;s5ziAnG5rk0gfBsWxA7Y&quot;,&quot;name&quot;:&quot;app.filament.widgets.api-health-status&quot;,&quot;path&quot;:&quot;admin&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;lazyLoaded&quot;:false,&quot;lazyIsolated&quot;:true,&quot;errors&quot;:[],&quot;locale&quot;:&quot;ar&quot;},&quot;checksum&quot;:&quot;8cc0b3b1c63bf02547c26f5d4ea3b49763799ca8eee4274e6a9310579dd31369&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"10 characters\">__lazyLoad</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"344 characters\">eyJkYXRhIjp7ImZvck1vdW50IjpbeyJmaWx0ZXJzIjpbeyJsaWJyYXJ5IjpudWxsLCJzdGFydERhdGUiOm51bGwsImVuZERhdGUiOm51bGx9LHsicyI6ImFyciJ9XX0seyJzIjoiYXJyIn1dfSwibWVtbyI6eyJpZCI6IkljMzRUQ0hNanNVU2h5dVpyUU9LIiwibmFtZSI6Il9fbW91bnRQYXJhbXNDb250YWluZXIifSwiY2hlY2tzdW0iOiI3YmUyM2VlNjI1MWVhN2ViNWNlN2E5YWI4NjdhYTkxNzUyNzdhMDcwMzFkYTBiYjE2NTE2MGYxNjEyM2NhZTAzIn0=</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-704349270\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1329015090 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1555 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjhlNFJweU9qTWYyaXc4SEtWMDl3OGc9PSIsInZhbHVlIjoibnJRVzFVMC9nV09rS2xUUjNERDZJVG9TVlM3M3Q1RXU0S3hVMW9QRVRpMlQrc25qSm9UbFIwV1kzSzNqTUtaVU1xNExkV09NZHFLSjIyR1M3NTB1enJzbDI2bmRabmFON0VYSlN5OTFaMUdDUVVUSmMyYW15RmtjSmhtUmgyZVkzclRrTXd6eWpKbG5DUzFPY0JmaE1KQzRDVjUzQkp4Ukl4aUwwOXlRZXN6dllPL2dPbWllZE42QTRKbGFuczNMNVFENmlsaXVZb3lmcHNkZkRMejJqZDQzSHd3V3N2cU13MDJKbFJoZ0RkST0iLCJtYWMiOiI2NTRlYzEyZTY5ODY1MzRlMmY4YmM5MTM4MTdhN2Q3YWQwYzQzZTZiOGQ3YmFmZDg3YWFjMzJhMTgyYzBiMTc3IiwidGFnIjoiIn0%3D; filament_language_switch_locale=eyJpdiI6IldSalJ4dW1CcFhqWCtSR2pFSytLYUE9PSIsInZhbHVlIjoiQTVEaTRtaXhMVlhxNTBXZEUrOSt3UDVoQUJvOGdCa21ZODBwekJ5aDc5bjJ2SzgySVR1YnpMOVNWWlpSdjVWcyIsIm1hYyI6ImUyNjI0NmUyMzk3MjI2MmUyNzdiY2JhOGVhMzE0YjRjOWIyNzJiODUxMGU1NzQzYTJhY2M1YjIwYjc0ZTJkOTgiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6InF5bE5QcmVvejJYMVN1QW1FaWxpdFE9PSIsInZhbHVlIjoiQlJpVVl1V214UG1QSTBvcUZtdzZ4SGZub2FPRFhjMHU4YlRtMjRFeHBYck5xamRLaVROU2JLd1RhQy9jTUtmTnBjZ3hEbWx2YUtJRm5Zdm9NSVJoOC9mWmxhaUdOWGVsQzV3ek9SVWp3a0c4Yk8xMnk3ODB4WUpuK2Y2Vlh4bjQiLCJtYWMiOiIwMzljMTUzNDVjZDVlYmU4MDRjYzhjNGUzNGE3ZDgyZDhlNTlmODQ1MGIxODExYWI4MDgwNjRmMGUyM2JlZWE1IiwidGFnIjoiIn0%3D; alfhrs_almohd_session=eyJpdiI6IkF1TXc4VnJjWU5rWU9oQnRaanRlR1E9PSIsInZhbHVlIjoiMFFMZmdLemIrdk5LaE5JdTZKWlVMRFdRVS9MZFNYak5EOW9VVGVNUkVVTzNHWDJPWUFjUHk1d2l0aStmQkdIbWwrWFVNQ1VINkNCdjlCMzgwOWlJZXVPOWEvNmcwTnY4M2Q0OTRuNDlVMDd6WkdVamRFVmhVenFEci8zSnNxZm4iLCJtYWMiOiJlOGFlMDZmOWUyYmZjYTFkMjcyZGFkZTlmMmY1NzZjYmVlOWY1YTU0ZWQzNjIxMzIzODUwZjUxN2Q1MGFjZDBjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">ar-EG,ar;q=0.9,en-US;q=0.8,en;q=0.7,fr;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://bplpnet.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">http://bplpnet.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">847</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">bplpnet.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1329015090\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-36450828 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"124 characters\">23|oayZmX4hCkVRzDYgX8XiyDlbdUWGhjg8WRylkmFAigbARBfuImkVfTcPvqxq|$2y$10$ndp0mY0vfh7SKC/wbKghcOK8n.7TdyC6/Z5j84PViILPym72FiJWy</span>\"\n  \"<span class=sf-dump-key>filament_language_switch_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG</span>\"\n  \"<span class=sf-dump-key>alfhrs_almohd_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OHrIdfK5jqy65dnTOAgZp53RllI0Bol0hjZv4x8G</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-36450828\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2062708916 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 03 Jul 2025 06:51:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2062708916\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1801312717 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://bplpnet.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$10$ndp0mY0vfh7SKC/wbKghcOK8n.7TdyC6/Z5j84PViILPym72FiJWy</span>\"\n  \"<span class=sf-dump-key>48040ef7f2542b39b9ba9a72983b0d88_filters</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>library</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>startDate</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>endDate</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1801312717\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://bplpnet.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}