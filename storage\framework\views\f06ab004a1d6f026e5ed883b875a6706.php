<?php if (isset($component)) { $__componentOriginald489e48d6214ecaf87e4b6a8ce684ad1 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald489e48d6214ecaf87e4b6a8ce684ad1 = $attributes; } ?>
<?php $component = Filament\View\LegacyComponents\Widget::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::widget'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Filament\View\LegacyComponents\Widget::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <?php if (isset($component)) { $__componentOriginal9b945b32438afb742355861768089b04 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9b945b32438afb742355861768089b04 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.card','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
        <div class="flex items-center justify-between">
            <h2 class="text-lg font-bold dark:text-white"><?php echo e(__('widgets.api_health_status.title')); ?></h2>
        </div>
        
        <div class="mt-4 space-y-4">
            <?php
                $healthData = $this->getLibrariesHealthStatus();
                $isRtl = app()->getLocale() === 'ar' || app()->getLocale() === 'he' || app()->getLocale() === 'fa';
            ?>
            
            <!--[if BLOCK]><![endif]--><?php if(count($healthData) === 0): ?>
                <div class="p-4 text-center text-gray-500 dark:text-gray-400">
                    <?php echo e(__('widgets.api_health_status.no_data')); ?>

                </div>
            <?php else: ?>
                <div class="overflow-x-auto">
                    <table class="w-full text-sm <?php echo e($isRtl ? 'text-right' : 'text-left'); ?> dark:text-gray-200">
                        <thead>
                            <tr class="bg-gray-50 dark:bg-gray-700">
                                <th class="px-4 py-1"><?php echo e(__('widgets.api_health_status.columns.number')); ?></th>
                           
                                <th class="px-4 py-1"><?php echo e(__('widgets.api_health_status.columns.library')); ?></th>
                                
                                <th class="px-4 py-1"><?php echo e(__('widgets.api_health_status.columns.ip_address')); ?></th>
                                <th class="px-4 py-1"><?php echo e(__('widgets.api_health_status.columns.status')); ?></th>
                                <th class="px-4 py-1"><?php echo e(__('widgets.api_health_status.columns.endpoints')); ?></th>
                                <th class="px-4 py-1"><?php echo e(__('widgets.api_health_status.columns.last_checked')); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $healthData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr class="border-t dark:border-gray-700">
                                    <td class="px-4 py-1"><?php echo e($index); ?></td>
          
                                    <td class="px-4 py-1"><span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">

<?php echo e($data['code'] ?? '-'); ?> 
</span> <?php echo e($data['name']); ?></td>
                                    
                                    <td class="px-4 py-1"><?php echo e($data['ip']); ?></td>
                                    <td class="px-4 py-1">
                                        <!--[if BLOCK]><![endif]--><?php if($data['overall_status']): ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                                                <?php echo e(__('widgets.api_health_status.status.healthy')); ?>

                                            </span>
                                        <?php else: ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100">
                                                <?php echo e(__('widgets.api_health_status.status.issues')); ?>

                                            </span>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </td>
                                    <td class="px-4 py-1">
                                        <div class="flex flex-wrap gap-1 <?php echo e($isRtl ? 'justify-end' : ''); ?>">
                                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $data['endpoints']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $endpoint => $info): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium <?php echo e($info['status'] 
                                                    ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' 
                                                    : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'); ?>">
                                                    <?php echo e(basename($endpoint)); ?>

                                                </span>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>
                                    </td>
                                    <td class="px-4 py-1">
                                        <?php echo e($data['last_checked'] ? $data['last_checked']->diffForHumans() : __('widgets.api_health_status.never')); ?>

                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </tbody>
                    </table>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9b945b32438afb742355861768089b04)): ?>
<?php $attributes = $__attributesOriginal9b945b32438afb742355861768089b04; ?>
<?php unset($__attributesOriginal9b945b32438afb742355861768089b04); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9b945b32438afb742355861768089b04)): ?>
<?php $component = $__componentOriginal9b945b32438afb742355861768089b04; ?>
<?php unset($__componentOriginal9b945b32438afb742355861768089b04); ?>
<?php endif; ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald489e48d6214ecaf87e4b6a8ce684ad1)): ?>
<?php $attributes = $__attributesOriginald489e48d6214ecaf87e4b6a8ce684ad1; ?>
<?php unset($__attributesOriginald489e48d6214ecaf87e4b6a8ce684ad1); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald489e48d6214ecaf87e4b6a8ce684ad1)): ?>
<?php $component = $__componentOriginald489e48d6214ecaf87e4b6a8ce684ad1; ?>
<?php unset($__componentOriginald489e48d6214ecaf87e4b6a8ce684ad1); ?>
<?php endif; ?><?php /**PATH C:\laragon\www\BplpNet\resources\views/filament/widgets/api-health-status.blade.php ENDPATH**/ ?>