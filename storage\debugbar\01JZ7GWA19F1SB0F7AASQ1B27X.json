{"__meta": {"id": "01JZ7GWA19F1SB0F7AASQ1B27X", "datetime": "2025-07-03 07:51:40", "utime": **********.970353, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.4.4", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.457612, "end": **********.970384, "duration": 0.5127718448638916, "duration_str": "513ms", "measures": [{"label": "Booting", "start": **********.457612, "relative_start": 0, "end": **********.749442, "relative_end": **********.749442, "duration": 0.*****************, "duration_str": "292ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.749454, "relative_start": 0.***************, "end": **********.970386, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "221ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.760298, "relative_start": 0.****************, "end": **********.762413, "relative_end": **********.762413, "duration": 0.002115011215209961, "duration_str": "2.12ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.967765, "relative_start": 0.**************, "end": **********.968576, "relative_end": **********.968576, "duration": 0.0008108615875244141, "duration_str": "811μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "40MB"}, "exceptions": {"count": 0, "exceptions": []}, "route": {"uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>"}, "queries": {"count": 86, "nb_statements": 85, "nb_visible_statements": 86, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.06435000000000005, "accumulated_duration_str": "64.35ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, {"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Support/Utils.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Support\\Utils.php", "line": 114}, {"index": 17, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasPanelShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasPanelShield.php", "line": 17}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 208}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 54}], "start": **********.782263, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "FilamentShield.php:121", "source": {"index": 15, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FFilamentShield.php&line=121", "ajax": false, "filename": "FilamentShield.php", "line": "121"}, "connection": "mcabplpnet", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from \"roles\" where (\"name\" = 'panel_user') limit 1", "type": "query", "params": [], "bindings": ["panel_user"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, {"index": 21, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Support/Utils.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Support\\Utils.php", "line": 114}, {"index": 22, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasPanelShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasPanelShield.php", "line": 17}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 208}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 54}], "start": **********.782979, "duration": 0.02844, "duration_str": "28.44ms", "memory": 0, "memory_str": null, "filename": "FilamentShield.php:121", "source": {"index": 20, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FFilamentShield.php&line=121", "ajax": false, "filename": "FilamentShield.php", "line": "121"}, "connection": "mcabplpnet", "explain": null, "start_percent": 0, "width_percent": 44.196}, {"sql": "select * from \"users\" where \"id\" = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.812673, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "mcabplpnet", "explain": null, "start_percent": 44.196, "width_percent": 2.051}, {"sql": "select * from \"libraries\" where \"status\" = 'Open' and \"libraries\".\"deleted_at\" is null", "type": "query", "params": [], "bindings": ["Open"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 19}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.832904, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:19", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=19", "ajax": false, "filename": "ApiHealthStatus.php", "line": "19"}, "connection": "mcabplpnet", "explain": null, "start_percent": 46.247, "width_percent": 2.16}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 1 group by \"endpoint\"", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.8374271, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 48.407, "width_percent": 2.626}, {"sql": "select * from \"api_health_checks\" where \"id\" in (2, 3, 1)", "type": "query", "params": [], "bindings": [2, 3, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.84011, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 51.033, "width_percent": 0.699}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 2 group by \"endpoint\"", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.842535, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 51.733, "width_percent": 0.684}, {"sql": "select * from \"api_health_checks\" where \"id\" in (5, 6, 4)", "type": "query", "params": [], "bindings": [5, 6, 4], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.8446422, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 52.416, "width_percent": 0.715}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 3 group by \"endpoint\"", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.8461251, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 53.131, "width_percent": 0.435}, {"sql": "select * from \"api_health_checks\" where \"id\" in (8, 9, 7)", "type": "query", "params": [], "bindings": [8, 9, 7], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.8472261, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 53.566, "width_percent": 0.435}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 4 group by \"endpoint\"", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.8484771, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 54.002, "width_percent": 0.746}, {"sql": "select * from \"api_health_checks\" where \"id\" in (11, 12, 10)", "type": "query", "params": [], "bindings": [11, 12, 10], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.850144, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 54.747, "width_percent": 0.653}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 5 group by \"endpoint\"", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.851754, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 55.4, "width_percent": 0.855}, {"sql": "select * from \"api_health_checks\" where \"id\" in (14, 15, 13)", "type": "query", "params": [], "bindings": [14, 15, 13], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.8531861, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 56.255, "width_percent": 0.513}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 6 group by \"endpoint\"", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.85487, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 56.768, "width_percent": 0.839}, {"sql": "select * from \"api_health_checks\" where \"id\" in (17, 18, 16)", "type": "query", "params": [], "bindings": [17, 18, 16], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.8567069, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 57.607, "width_percent": 0.653}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 7 group by \"endpoint\"", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.858929, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 58.26, "width_percent": 0.606}, {"sql": "select * from \"api_health_checks\" where \"id\" in (20, 21, 19)", "type": "query", "params": [], "bindings": [20, 21, 19], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.8607461, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 58.866, "width_percent": 0.761}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 8 group by \"endpoint\"", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.86213, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 59.627, "width_percent": 0.591}, {"sql": "select * from \"api_health_checks\" where \"id\" in (23, 24, 22)", "type": "query", "params": [], "bindings": [23, 24, 22], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.863316, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 60.218, "width_percent": 0.42}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 9 group by \"endpoint\"", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.864757, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 60.637, "width_percent": 0.761}, {"sql": "select * from \"api_health_checks\" where \"id\" in (26, 27, 25)", "type": "query", "params": [], "bindings": [26, 27, 25], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.866261, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 61.399, "width_percent": 0.668}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 10 group by \"endpoint\"", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.868376, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 62.067, "width_percent": 0.761}, {"sql": "select * from \"api_health_checks\" where \"id\" in (29, 30, 28)", "type": "query", "params": [], "bindings": [29, 30, 28], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.8703332, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 62.828, "width_percent": 0.73}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 11 group by \"endpoint\"", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.8718312, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 63.559, "width_percent": 0.715}, {"sql": "select * from \"api_health_checks\" where \"id\" in (32, 33, 31)", "type": "query", "params": [], "bindings": [32, 33, 31], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.873903, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 64.274, "width_percent": 0.684}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 12 group by \"endpoint\"", "type": "query", "params": [], "bindings": [12], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.875323, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 64.957, "width_percent": 0.684}, {"sql": "select * from \"api_health_checks\" where \"id\" in (35, 36, 34)", "type": "query", "params": [], "bindings": [35, 36, 34], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.877498, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 65.641, "width_percent": 0.808}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 13 group by \"endpoint\"", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.879201, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 66.449, "width_percent": 0.855}, {"sql": "select * from \"api_health_checks\" where \"id\" in (38, 39, 37)", "type": "query", "params": [], "bindings": [38, 39, 37], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.881006, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 67.304, "width_percent": 0.824}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 14 group by \"endpoint\"", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.883173, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 68.127, "width_percent": 0.824}, {"sql": "select * from \"api_health_checks\" where \"id\" in (41, 42, 40)", "type": "query", "params": [], "bindings": [41, 42, 40], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.8846169, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 68.951, "width_percent": 0.637}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 15 group by \"endpoint\"", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.8859491, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 69.588, "width_percent": 0.497}, {"sql": "select * from \"api_health_checks\" where \"id\" in (44, 45, 43)", "type": "query", "params": [], "bindings": [44, 45, 43], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.887275, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 70.085, "width_percent": 0.497}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 17 group by \"endpoint\"", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.8885329, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 70.583, "width_percent": 0.575}, {"sql": "select * from \"api_health_checks\" where \"id\" in (47, 48, 46)", "type": "query", "params": [], "bindings": [47, 48, 46], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.889708, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 71.158, "width_percent": 0.42}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 18 group by \"endpoint\"", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.890798, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 71.577, "width_percent": 0.684}, {"sql": "select * from \"api_health_checks\" where \"id\" in (50, 49)", "type": "query", "params": [], "bindings": [50, 49], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.892188, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 72.261, "width_percent": 0.497}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 19 group by \"endpoint\"", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.8934512, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 72.758, "width_percent": 0.42}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.8945298, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 73.178, "width_percent": 0.404}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 20 group by \"endpoint\"", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.895542, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 73.582, "width_percent": 0.357}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.896569, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 73.939, "width_percent": 0.497}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 21 group by \"endpoint\"", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.8976529, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 74.437, "width_percent": 0.389}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.898643, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 74.825, "width_percent": 0.295}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 23 group by \"endpoint\"", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.9005, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 75.12, "width_percent": 0.559}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.901655, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 75.68, "width_percent": 0.326}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 24 group by \"endpoint\"", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.90283, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 76.006, "width_percent": 0.559}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.903996, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 76.566, "width_percent": 0.451}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 25 group by \"endpoint\"", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.905139, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 77.016, "width_percent": 0.808}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.906459, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 77.824, "width_percent": 0.528}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 26 group by \"endpoint\"", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.9076262, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 78.353, "width_percent": 0.684}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.908869, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 79.037, "width_percent": 0.466}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 27 group by \"endpoint\"", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.9109042, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 79.503, "width_percent": 0.746}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.912332, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 80.249, "width_percent": 0.746}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 28 group by \"endpoint\"", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.9141362, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 80.995, "width_percent": 0.73}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.915759, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 81.725, "width_percent": 0.528}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 29 group by \"endpoint\"", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.9169822, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 82.253, "width_percent": 0.73}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.91927, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 82.984, "width_percent": 0.855}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 30 group by \"endpoint\"", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.9208088, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 83.838, "width_percent": 0.668}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.922155, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 84.507, "width_percent": 0.544}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 31 group by \"endpoint\"", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.9234369, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 85.051, "width_percent": 0.653}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.924808, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 85.703, "width_percent": 0.528}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 32 group by \"endpoint\"", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.926095, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 86.232, "width_percent": 0.653}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.927323, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 86.884, "width_percent": 0.715}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 33 group by \"endpoint\"", "type": "query", "params": [], "bindings": [33], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.928863, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 87.599, "width_percent": 0.684}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.930203, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 88.283, "width_percent": 0.591}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 34 group by \"endpoint\"", "type": "query", "params": [], "bindings": [34], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.9318972, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 88.873, "width_percent": 0.963}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.933765, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 89.837, "width_percent": 0.622}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 35 group by \"endpoint\"", "type": "query", "params": [], "bindings": [35], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.935123, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 90.458, "width_percent": 0.668}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.937003, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 91.127, "width_percent": 0.544}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 36 group by \"endpoint\"", "type": "query", "params": [], "bindings": [36], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.939419, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 91.671, "width_percent": 0.715}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.940802, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 92.385, "width_percent": 0.435}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 37 group by \"endpoint\"", "type": "query", "params": [], "bindings": [37], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.9418921, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 92.821, "width_percent": 0.513}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.943111, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 93.333, "width_percent": 0.528}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 38 group by \"endpoint\"", "type": "query", "params": [], "bindings": [38], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.944795, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 93.862, "width_percent": 0.808}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.946243, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 94.67, "width_percent": 0.591}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 39 group by \"endpoint\"", "type": "query", "params": [], "bindings": [39], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.947639, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 95.26, "width_percent": 0.435}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.949085, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 95.695, "width_percent": 0.575}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 40 group by \"endpoint\"", "type": "query", "params": [], "bindings": [40], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.950514, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 96.27, "width_percent": 0.466}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.951885, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 96.737, "width_percent": 0.342}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 43 group by \"endpoint\"", "type": "query", "params": [], "bindings": [43], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.952909, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 97.078, "width_percent": 0.357}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.954359, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 97.436, "width_percent": 0.42}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 44 group by \"endpoint\"", "type": "query", "params": [], "bindings": [44], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.95542, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 97.855, "width_percent": 0.544}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.956537, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 98.399, "width_percent": 0.326}, {"sql": "select \"endpoint\", MAX(id) as id from \"api_health_checks\" where \"library_id\" = 45 group by \"endpoint\"", "type": "query", "params": [], "bindings": [45], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.9576678, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:28", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=28", "ajax": false, "filename": "ApiHealthStatus.php", "line": "28"}, "connection": "mcabplpnet", "explain": null, "start_percent": 98.726, "width_percent": 0.622}, {"sql": "select * from \"api_health_checks\" where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, {"index": 16, "namespace": "view", "name": "filament.widgets.api-health-status", "file": "C:\\laragon\\www\\BplpNet\\resources\\views/filament/widgets/api-health-status.blade.php", "line": 27}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.959061, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ApiHealthStatus.php:31", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/ApiHealthStatus.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Widgets\\ApiHealthStatus.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FWidgets%2FApiHealthStatus.php&line=31", "ajax": false, "filename": "ApiHealthStatus.php", "line": "31"}, "connection": "mcabplpnet", "explain": null, "start_percent": 99.347, "width_percent": 0.653}]}, "models": {"data": {"App\\Models\\ApiHealthCheck": {"value": 100, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FModels%2FApiHealthCheck.php&line=1", "ajax": false, "filename": "ApiHealthCheck.php", "line": "?"}}, "App\\Models\\Library": {"value": 41, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FModels%2FLibrary.php&line=1", "ajax": false, "filename": "Library.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 143, "is_counter": true}, "livewire": {"data": {"app.filament.widgets.api-health-status #Lryo8LJQOLCIlLvnVSWw": "array:4 [\n  \"data\" => []\n  \"name\" => \"app.filament.widgets.api-health-status\"\n  \"component\" => \"App\\Filament\\Widgets\\ApiHealthStatus\"\n  \"id\" => \"Lryo8LJQOLCIlLvnVSWw\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG", "PHPDEBUGBAR_STACK_DATA": "[]", "_previous": "array:1 [\n  \"url\" => \"http://bplpnet.test/admin\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "23", "password_hash_web": "$2y$10$ndp0mY0vfh7SKC/wbKghcOK8n.7TdyC6/Z5j84PViILPym72FiJWy", "48040ef7f2542b39b9ba9a72983b0d88_filters": "array:3 [\n  \"library\" => null\n  \"startDate\" => null\n  \"endDate\" => null\n]"}, "request": {"data": {"status": "200 OK", "full_url": "http://bplpnet.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "telescope": "<a href=\"http://bplpnet.test/_debugbar/telescope/9f4ce187-b82d-4002-859c-ce9794f861a2\" target=\"_blank\">View in Telescope</a>", "duration": "522ms", "peak_memory": "44MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1095694957 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1095694957\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1263897507 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"308 characters\">{&quot;data&quot;:[],&quot;memo&quot;:{&quot;id&quot;:&quot;Lryo8LJQOLCIlLvnVSWw&quot;,&quot;name&quot;:&quot;app.filament.widgets.api-health-status&quot;,&quot;path&quot;:&quot;admin&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;lazyLoaded&quot;:false,&quot;lazyIsolated&quot;:true,&quot;errors&quot;:[],&quot;locale&quot;:&quot;ar&quot;},&quot;checksum&quot;:&quot;28ad0cc0fcd3251ec586b4cca93d7b30f271b4c537c1ca84d25260c2ef2e2cf7&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"10 characters\">__lazyLoad</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"344 characters\">eyJkYXRhIjp7ImZvck1vdW50IjpbeyJmaWx0ZXJzIjpbeyJsaWJyYXJ5IjpudWxsLCJzdGFydERhdGUiOm51bGwsImVuZERhdGUiOm51bGx9LHsicyI6ImFyciJ9XX0seyJzIjoiYXJyIn1dfSwibWVtbyI6eyJpZCI6InVGdjFraGZNaWNUUE9xTEVVZTY5IiwibmFtZSI6Il9fbW91bnRQYXJhbXNDb250YWluZXIifSwiY2hlY2tzdW0iOiJmZjQ5ZWM0NWVhNTdkODU1YjdjZjE2MjgxZmY0MTRkZWY5OTBkMTk4YWE0OTUxZTdmNDg0YmY1N2Q1ZTFhZWU5In0=</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1263897507\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2136396379 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1555 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjhlNFJweU9qTWYyaXc4SEtWMDl3OGc9PSIsInZhbHVlIjoibnJRVzFVMC9nV09rS2xUUjNERDZJVG9TVlM3M3Q1RXU0S3hVMW9QRVRpMlQrc25qSm9UbFIwV1kzSzNqTUtaVU1xNExkV09NZHFLSjIyR1M3NTB1enJzbDI2bmRabmFON0VYSlN5OTFaMUdDUVVUSmMyYW15RmtjSmhtUmgyZVkzclRrTXd6eWpKbG5DUzFPY0JmaE1KQzRDVjUzQkp4Ukl4aUwwOXlRZXN6dllPL2dPbWllZE42QTRKbGFuczNMNVFENmlsaXVZb3lmcHNkZkRMejJqZDQzSHd3V3N2cU13MDJKbFJoZ0RkST0iLCJtYWMiOiI2NTRlYzEyZTY5ODY1MzRlMmY4YmM5MTM4MTdhN2Q3YWQwYzQzZTZiOGQ3YmFmZDg3YWFjMzJhMTgyYzBiMTc3IiwidGFnIjoiIn0%3D; filament_language_switch_locale=eyJpdiI6IldSalJ4dW1CcFhqWCtSR2pFSytLYUE9PSIsInZhbHVlIjoiQTVEaTRtaXhMVlhxNTBXZEUrOSt3UDVoQUJvOGdCa21ZODBwekJ5aDc5bjJ2SzgySVR1YnpMOVNWWlpSdjVWcyIsIm1hYyI6ImUyNjI0NmUyMzk3MjI2MmUyNzdiY2JhOGVhMzE0YjRjOWIyNzJiODUxMGU1NzQzYTJhY2M1YjIwYjc0ZTJkOTgiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6IjBUc1kyTHM3cStzU1I1WFZrTzRIbVE9PSIsInZhbHVlIjoiOW5Hc2orRERYd3EzOTJ4M3RBdnVPMFFNaW5RbFNUME5ZTTFaY1FDUXdNaEVpZ0hUUDZ1WmtFSzZLSnhaSGFCQ1VrcEhxL0RwZ0VjejBMVmpDTDJsNFFXVllxWE9qSnB6UEZocGp1TjlLR3NualBwaFE4d1FRWUZsdHBsOEhSQXEiLCJtYWMiOiJkOTVjYjA0ZGIzZGRkZWYzZGM2ZGFiN2U0ZWNkYzliNmFiZmJmZmEyNzllYzVkNzRmODNhNjUxMjFmY2JmZGE1IiwidGFnIjoiIn0%3D; alfhrs_almohd_session=eyJpdiI6IndOeHppakFGSmIxRG9LNzVpNUliVlE9PSIsInZhbHVlIjoicC9KNEp0TVVxMjNqb2VIdjdsOW9Da0xXWU15SG5idmlwN2RwQXJRUjh6MVc4TXdHU1UvNUxOWUl3TmtxNWtXTk1zbnNvVk5OZENCeHEvaXE2NlhlMkp5c09GemwwWk9GdEFPU3Fld0hwS0llVTR3U0dGait1ZFk1S214R01HVEYiLCJtYWMiOiI0ZjZlODgzNDEzYTdlMWJlMTQ4YzczODBjYWExMmYwMjI4N2VmZTJmMTczNTlhNjZhNWMyNGQ3M2Q2MTU0Yzk2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">ar-EG,ar;q=0.9,en-US;q=0.8,en;q=0.7,fr;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://bplpnet.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">http://bplpnet.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">847</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">bplpnet.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2136396379\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1081491647 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"124 characters\">23|oayZmX4hCkVRzDYgX8XiyDlbdUWGhjg8WRylkmFAigbARBfuImkVfTcPvqxq|$2y$10$ndp0mY0vfh7SKC/wbKghcOK8n.7TdyC6/Z5j84PViILPym72FiJWy</span>\"\n  \"<span class=sf-dump-key>filament_language_switch_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG</span>\"\n  \"<span class=sf-dump-key>alfhrs_almohd_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OHrIdfK5jqy65dnTOAgZp53RllI0Bol0hjZv4x8G</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1081491647\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-560735441 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 03 Jul 2025 06:51:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-560735441\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1795960328 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://bplpnet.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$10$ndp0mY0vfh7SKC/wbKghcOK8n.7TdyC6/Z5j84PViILPym72FiJWy</span>\"\n  \"<span class=sf-dump-key>48040ef7f2542b39b9ba9a72983b0d88_filters</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>library</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>startDate</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>endDate</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1795960328\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://bplpnet.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}