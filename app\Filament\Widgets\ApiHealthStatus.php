<?php

namespace App\Filament\Widgets;

use App\Models\ApiHealthCheck;
use App\Models\Library;
use Filament\Widgets\Widget;
use Illuminate\Support\Facades\DB;

class ApiHealthStatus extends Widget
{
    protected static string $view = 'filament.widgets.api-health-status';

    protected int|string|array $columnSpan = 'full';
    protected static ?int $sort = 3;

    public function getLibrariesHealthStatus()
    {
        $libraries = Library::where('status', 'Open')->get();
        $healthData = [];

        foreach ($libraries as $library) {
            // Get the latest check for each endpoint for this library
            $latestChecks = ApiHealthCheck::where('library_id', $library->id)
                ->select('endpoint',
                    DB::raw('MAX(id) as id'))
                ->groupBy('endpoint')
                ->get()
                ->pluck('id');

            $checks = ApiHealthCheck::whereIn('id', $latestChecks)->get();

            $endpoints = [];
            $overallStatus = true;

            foreach ($checks as $check) {
                $endpoints[$check->endpoint] = [
                    'status' => $check->status,
                    'response_time' => $check->response_time,
                    'response_code' => $check->response_code,
                    'checked_at' => $check->checked_at,
                ];

                if (! $check->status) {
                    $overallStatus = false;
                }
            }

            $healthData[$library->id] = [
                'name' => $library->name,
                'code' => $library->code,
                'ip' => $library->ip_adress,
                'overall_status' => $overallStatus,
                'endpoints' => $endpoints,
                'last_checked' => $checks->isNotEmpty() ? $checks->sortByDesc('checked_at')->first()->checked_at : null,
            ];
        }

        return $healthData;
    }
}
