{"__meta": {"id": "01JZ7HMHPYABE7SBP7R2BE29D6", "datetime": "2025-07-03 08:04:55", "utime": **********.262962, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.4.4", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751526294.834958, "end": **********.26301, "duration": 0.4280519485473633, "duration_str": "428ms", "measures": [{"label": "Booting", "start": 1751526294.834958, "relative_start": 0, "end": **********.120616, "relative_end": **********.120616, "duration": 0.****************, "duration_str": "286ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.120629, "relative_start": 0.****************, "end": **********.263013, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "142ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.130238, "relative_start": 0.*****************, "end": **********.132994, "relative_end": **********.132994, "duration": 0.002755880355834961, "duration_str": "2.76ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.259561, "relative_start": 0.****************, "end": **********.26065, "relative_end": **********.26065, "duration": 0.001088857650756836, "duration_str": "1.09ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "route": {"uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>"}, "queries": {"count": 5, "nb_statements": 4, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.035780000000000006, "accumulated_duration_str": "35.78ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, {"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Support/Utils.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Support\\Utils.php", "line": 114}, {"index": 17, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasPanelShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasPanelShield.php", "line": 17}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 208}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 54}], "start": **********.157528, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "FilamentShield.php:121", "source": {"index": 15, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FFilamentShield.php&line=121", "ajax": false, "filename": "FilamentShield.php", "line": "121"}, "connection": "mcabplpnet", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from \"roles\" where (\"name\" = 'panel_user') limit 1", "type": "query", "params": [], "bindings": ["panel_user"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, {"index": 21, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Support/Utils.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Support\\Utils.php", "line": 114}, {"index": 22, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasPanelShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasPanelShield.php", "line": 17}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 208}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 54}], "start": **********.158187, "duration": 0.03146, "duration_str": "31.46ms", "memory": 0, "memory_str": null, "filename": "FilamentShield.php:121", "source": {"index": 20, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FFilamentShield.php&line=121", "ajax": false, "filename": "FilamentShield.php", "line": "121"}, "connection": "mcabplpnet", "explain": null, "start_percent": 0, "width_percent": 87.926}, {"sql": "select * from \"users\" where \"id\" = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.190608, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "mcabplpnet", "explain": null, "start_percent": 87.926, "width_percent": 2.487}, {"sql": "select * from \"libraries\" where \"libraries\".\"deleted_at\" is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Livewire/InteractiveMap.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Livewire\\InteractiveMap.php", "line": 22}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.211375, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "InteractiveMap.php:22", "source": {"index": 15, "namespace": null, "name": "app/Livewire/InteractiveMap.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Livewire\\InteractiveMap.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FLivewire%2FInteractiveMap.php&line=22", "ajax": false, "filename": "InteractiveMap.php", "line": "22"}, "connection": "mcabplpnet", "explain": null, "start_percent": 90.414, "width_percent": 3.689}, {"sql": "select * from \"wilayas\" where \"wilayas\".\"id\" in (1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20, 21, 22, 23, 25, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Livewire/InteractiveMap.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Livewire\\InteractiveMap.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.216389, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "InteractiveMap.php:22", "source": {"index": 20, "namespace": null, "name": "app/Livewire/InteractiveMap.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Livewire\\InteractiveMap.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FLivewire%2FInteractiveMap.php&line=22", "ajax": false, "filename": "InteractiveMap.php", "line": "22"}, "connection": "mcabplpnet", "explain": null, "start_percent": 94.103, "width_percent": 5.897}]}, "models": {"data": {"App\\Models\\Library": {"value": 45, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FModels%2FLibrary.php&line=1", "ajax": false, "filename": "Library.php", "line": "?"}}, "App\\Models\\Wilaya": {"value": 44, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FModels%2FWilaya.php&line=1", "ajax": false, "filename": "Wilaya.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 91, "is_counter": true}, "livewire": {"data": {"app.filament.widgets.algeria-map-widget #UDFzzX0rtvjiwb98LKtl": "array:4 [\n  \"data\" => []\n  \"name\" => \"app.filament.widgets.algeria-map-widget\"\n  \"component\" => \"App\\Filament\\Widgets\\AlgeriaMapWidget\"\n  \"id\" => \"UDFzzX0rtvjiwb98LKtl\"\n]", "interactive-map #FeX4gslLGrmMB8XiKcD9": "array:4 [\n  \"data\" => array:6 [\n    \"libraries\" => Illuminate\\Database\\Eloquent\\Collection {#2380\n      #items: array:45 [\n        0 => App\\Models\\Library {#2560\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [\n            \"id\" => 1\n            \"name\" => \" تمنراست\"\n            \"minister\" => null\n            \"wilaya_id\" => \"11\"\n            \"municipality_id\" => null\n            \"code\" => \"1101\"\n            \"status\" => \"Open\"\n            \"agent_name\" => \"بن دايس عبد الوهاب\"\n            \"ip_adress\" => \"*************\"\n            \"api_port\" => \"4438\"\n            \"api_end\" => \"/api\"\n            \"books_end_point\" => \"/api/books\"\n            \"readers_end_point\" => \"/api/reader\"\n            \"stats_end_point\" => \"/api/stasts\"\n            \"api_version\" => \"1\"\n            \"software\" => \"pmb\"\n            \"adress\" => null\n            \"phone\" => null\n            \"fax\" => null\n            \"email\" => null\n            \"website_url\" => null\n            \"facebook_url\" => null\n            \"notes\" => \"قيد التجريب\"\n            \"created_at\" => null\n            \"updated_at\" => \"2025-01-13 06:51:17\"\n            \"deleted_at\" => null\n          ]\n          #original: array:26 [\n            \"id\" => 1\n            \"name\" => \" تمنراست\"\n            \"minister\" => null\n            \"wilaya_id\" => \"11\"\n            \"municipality_id\" => null\n            \"code\" => \"1101\"\n            \"status\" => \"Open\"\n            \"agent_name\" => \"بن دايس عبد الوهاب\"\n            \"ip_adress\" => \"*************\"\n            \"api_port\" => \"4438\"\n            \"api_end\" => \"/api\"\n            \"books_end_point\" => \"/api/books\"\n            \"readers_end_point\" => \"/api/reader\"\n            \"stats_end_point\" => \"/api/stasts\"\n            \"api_version\" => \"1\"\n            \"software\" => \"pmb\"\n            \"adress\" => null\n            \"phone\" => null\n            \"fax\" => null\n            \"email\" => null\n            \"website_url\" => null\n            \"facebook_url\" => null\n            \"notes\" => \"قيد التجريب\"\n            \"created_at\" => null\n            \"updated_at\" => \"2025-01-13 06:51:17\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [\n            \"wilaya\" => App\\Models\\Wilaya {#2609 …36}\n          ]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [\n            0 => \"name\"\n            1 => \"code\"\n            2 => \"ip_adress\"\n            3 => \"api_port\"\n            4 => \"api_end\"\n            5 => \"books_end_point\"\n            6 => \"readers_end_point\"\n            7 => \"stats_end_point\"\n            8 => \"api_version\"\n            9 => \"status\"\n            10 => \"agent_name\"\n            11 => \"software\"\n            12 => \"wilaya_id\"\n            13 => \"municipality_id\"\n            14 => \"minister\"\n            15 => \"adress\"\n            16 => \"phone\"\n            17 => \"fax\"\n            18 => \"email\"\n            19 => \"website_url\"\n            20 => \"facebook_url\"\n            21 => \"notes\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: array:3 [\n            0 => \"created_at\"\n            1 => \"updated_at\"\n            2 => \"deleted_at\"\n          ]\n          +orderable: array:22 [\n            0 => \"id\"\n            1 => \"name\"\n            2 => \"code\"\n            3 => \"ip_adress\"\n            4 => \"api_port\"\n            5 => \"api_end\"\n            6 => \"books_end_point\"\n            7 => \"readers_end_point\"\n            8 => \"stats_end_point\"\n            9 => \"api_version\"\n            10 => \"software\"\n            11 => \"wilaya_id\"\n            12 => \"municipality_id\"\n            13 => \"minister\"\n            14 => \"adress\"\n            15 => \"status\"\n            16 => \"phone\"\n            17 => \"fax\"\n            18 => \"email\"\n            19 => \"website_url\"\n            20 => \"facebook_url\"\n            21 => \"notes\"\n          ]\n          +filterable: array:22 [\n            0 => \"id\"\n            1 => \"name\"\n            2 => \"code\"\n            3 => \"ip_adress\"\n            4 => \"api_port\"\n            5 => \"api_end\"\n            6 => \"books_end_point\"\n            7 => \"readers_end_point\"\n            8 => \"stats_end_point\"\n            9 => \"api_version\"\n            10 => \"software\"\n            11 => \"wilaya_id\"\n            12 => \"municipality_id\"\n            13 => \"minister\"\n            14 => \"adress\"\n            15 => \"status\"\n            16 => \"phone\"\n            17 => \"fax\"\n            18 => \"email\"\n            19 => \"website_url\"\n            20 => \"facebook_url\"\n            21 => \"notes\"\n          ]\n          #forceDeleting: false\n        }\n        1 => App\\Models\\Library {#2558\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [\n            \"id\" => 2\n            \"name\" => \" ادرار\"\n            \"minister\" => null\n            \"wilaya_id\" => \"1\"\n            \"municipality_id\" => \"1\"\n            \"code\" => \"0101\"\n            \"status\" => \"Open\"\n            \"agent_name\" => \"خالي علي مراد\"\n            \"ip_adress\" => \"*************\"\n            \"api_port\" => \"80\"\n            \"api_end\" => \"/api\"\n            \"books_end_point\" => \"/api/books\"\n            \"readers_end_point\" => \"/api/reader\"\n            \"stats_end_point\" => \"/api/stasts\"\n            \"api_version\" => \"1\"\n            \"software\" => \"fennec/syngeb\"\n            \"adress\" => null\n            \"phone\" => null\n            \"fax\" => null\n            \"email\" => null\n            \"website_url\" => null\n            \"facebook_url\" => null\n            \"notes\" => null\n            \"created_at\" => null\n            \"updated_at\" => \"2025-04-17 09:44:24\"\n            \"deleted_at\" => null\n          ]\n          #original: array:26 [\n            \"id\" => 2\n            \"name\" => \" ادرار\"\n            \"minister\" => null\n            \"wilaya_id\" => \"1\"\n            \"municipality_id\" => \"1\"\n            \"code\" => \"0101\"\n            \"status\" => \"Open\"\n            \"agent_name\" => \"خالي علي مراد\"\n            \"ip_adress\" => \"*************\"\n            \"api_port\" => \"80\"\n            \"api_end\" => \"/api\"\n            \"books_end_point\" => \"/api/books\"\n            \"readers_end_point\" => \"/api/reader\"\n            \"stats_end_point\" => \"/api/stasts\"\n            \"api_version\" => \"1\"\n            \"software\" => \"fennec/syngeb\"\n            \"adress\" => null\n            \"phone\" => null\n            \"fax\" => null\n            \"email\" => null\n            \"website_url\" => null\n            \"facebook_url\" => null\n            \"notes\" => null\n            \"created_at\" => null\n            \"updated_at\" => \"2025-04-17 09:44:24\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [\n            \"wilaya\" => App\\Models\\Wilaya {#2587 …36}\n          ]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [\n            0 => \"name\"\n            1 => \"code\"\n            2 => \"ip_adress\"\n            3 => \"api_port\"\n            4 => \"api_end\"\n            5 => \"books_end_point\"\n            6 => \"readers_end_point\"\n            7 => \"stats_end_point\"\n            8 => \"api_version\"\n            9 => \"status\"\n            10 => \"agent_name\"\n            11 => \"software\"\n            12 => \"wilaya_id\"\n            13 => \"municipality_id\"\n            14 => \"minister\"\n            15 => \"adress\"\n            16 => \"phone\"\n            17 => \"fax\"\n            18 => \"email\"\n            19 => \"website_url\"\n            20 => \"facebook_url\"\n            21 => \"notes\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: array:3 [\n            0 => \"created_at\"\n            1 => \"updated_at\"\n            2 => \"deleted_at\"\n          ]\n          +orderable: array:22 [\n            0 => \"id\"\n            1 => \"name\"\n            2 => \"code\"\n            3 => \"ip_adress\"\n            4 => \"api_port\"\n            5 => \"api_end\"\n            6 => \"books_end_point\"\n            7 => \"readers_end_point\"\n            8 => \"stats_end_point\"\n            9 => \"api_version\"\n            10 => \"software\"\n            11 => \"wilaya_id\"\n            12 => \"municipality_id\"\n            13 => \"minister\"\n            14 => \"adress\"\n            15 => \"status\"\n            16 => \"phone\"\n            17 => \"fax\"\n            18 => \"email\"\n            19 => \"website_url\"\n            20 => \"facebook_url\"\n            21 => \"notes\"\n          ]\n          +filterable: array:22 [\n            0 => \"id\"\n            1 => \"name\"\n            2 => \"code\"\n            3 => \"ip_adress\"\n            4 => \"api_port\"\n            5 => \"api_end\"\n            6 => \"books_end_point\"\n            7 => \"readers_end_point\"\n            8 => \"stats_end_point\"\n            9 => \"api_version\"\n            10 => \"software\"\n            11 => \"wilaya_id\"\n            12 => \"municipality_id\"\n            13 => \"minister\"\n            14 => \"adress\"\n            15 => \"status\"\n            16 => \"phone\"\n            17 => \"fax\"\n            18 => \"email\"\n            19 => \"website_url\"\n            20 => \"facebook_url\"\n            21 => \"notes\"\n          ]\n          #forceDeleting: false\n        }\n        2 => App\\Models\\Library {#2557\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [\n            \"id\" => 3\n            \"name\" => \"تلمسان\"\n            \"minister\" => null\n            \"wilaya_id\" => \"13\"\n            \"municipality_id\" => \"359\"\n            \"code\" => \"1301\"\n            \"status\" => \"Open\"\n            \"agent_name\" => \"يوسف موساوي\"\n            \"ip_adress\" => \"*************\"\n            \"api_port\" => \"56658\"\n            \"api_end\" => \"/api\"\n            \"books_end_point\" => \"/api/books\"\n            \"readers_end_point\" => \"/api/reader\"\n            \"stats_end_point\" => \"/api/stats\"\n            \"api_version\" => \"1\"\n            \"software\" => \"PMB\"\n            \"adress\" => null\n            \"phone\" => null\n            \"fax\" => null\n            \"email\" => null\n            \"website_url\" => null\n            \"facebook_url\" => null\n            \"notes\" => null\n            \"created_at\" => null\n            \"updated_at\" => \"2025-05-06 06:53:02\"\n            \"deleted_at\" => null\n          ]\n          #original: array:26 [\n            \"id\" => 3\n            \"name\" => \"تلمسان\"\n            \"minister\" => null\n            \"wilaya_id\" => \"13\"\n            \"municipality_id\" => \"359\"\n            \"code\" => \"1301\"\n            \"status\" => \"Open\"\n            \"agent_name\" => \"يوسف موساوي\"\n            \"ip_adress\" => \"*************\"\n            \"api_port\" => \"56658\"\n            \"api_end\" => \"/api\"\n            \"books_end_point\" => \"/api/books\"\n            \"readers_end_point\" => \"/api/reader\"\n            \"stats_end_point\" => \"/api/stats\"\n            \"api_version\" => \"1\"\n            \"software\" => \"PMB\"\n            \"adress\" => null\n            \"phone\" => null\n            \"fax\" => null\n            \"email\" => null\n            \"website_url\" => null\n            \"facebook_url\" => null\n            \"notes\" => null\n            \"created_at\" => null\n            \"updated_at\" => \"2025-05-06 06:53:02\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [\n            \"wilaya\" => App\\Models\\Wilaya {#2607 …36}\n          ]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [\n            0 => \"name\"\n            1 => \"code\"\n            2 => \"ip_adress\"\n            3 => \"api_port\"\n            4 => \"api_end\"\n            5 => \"books_end_point\"\n            6 => \"readers_end_point\"\n            7 => \"stats_end_point\"\n            8 => \"api_version\"\n            9 => \"status\"\n            10 => \"agent_name\"\n            11 => \"software\"\n            12 => \"wilaya_id\"\n            13 => \"municipality_id\"\n            14 => \"minister\"\n            15 => \"adress\"\n            16 => \"phone\"\n            17 => \"fax\"\n            18 => \"email\"\n            19 => \"website_url\"\n            20 => \"facebook_url\"\n            21 => \"notes\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: array:3 [\n            0 => \"created_at\"\n            1 => \"updated_at\"\n            2 => \"deleted_at\"\n          ]\n          +orderable: array:22 [\n            0 => \"id\"\n            1 => \"name\"\n            2 => \"code\"\n            3 => \"ip_adress\"\n            4 => \"api_port\"\n            5 => \"api_end\"\n            6 => \"books_end_point\"\n            7 => \"readers_end_point\"\n            8 => \"stats_end_point\"\n            9 => \"api_version\"\n            10 => \"software\"\n            11 => \"wilaya_id\"\n            12 => \"municipality_id\"\n            13 => \"minister\"\n            14 => \"adress\"\n            15 => \"status\"\n            16 => \"phone\"\n            17 => \"fax\"\n            18 => \"email\"\n            19 => \"website_url\"\n            20 => \"facebook_url\"\n            21 => \"notes\"\n          ]\n          +filterable: array:22 [\n            0 => \"id\"\n            1 => \"name\"\n            2 => \"code\"\n            3 => \"ip_adress\"\n            4 => \"api_port\"\n            5 => \"api_end\"\n            6 => \"books_end_point\"\n            7 => \"readers_end_point\"\n            8 => \"stats_end_point\"\n            9 => \"api_version\"\n            10 => \"software\"\n            11 => \"wilaya_id\"\n            12 => \"municipality_id\"\n            13 => \"minister\"\n            14 => \"adress\"\n            15 => \"status\"\n            16 => \"phone\"\n            17 => \"fax\"\n            18 => \"email\"\n            19 => \"website_url\"\n            20 => \"facebook_url\"\n            21 => \"notes\"\n          ]\n          #forceDeleting: false\n        }\n        3 => App\\Models\\Library {#2556\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [\n            \"id\" => 4\n            \"name\" => \" تيندوف\"\n            \"minister\" => null\n            \"wilaya_id\" => \"37\"\n            \"municipality_id\" => null\n            \"code\" => \"3701\"\n            \"status\" => \"Open\"\n            \"agent_name\" => \"بلولة لخضر\"\n            \"ip_adress\" => \"*************\"\n            \"api_port\" => \"45380\"\n            \"api_end\" => \"/api\"\n            \"books_end_point\" => \"/api/books\"\n            \"readers_end_point\" => \"/api/reader\"\n            \"stats_end_point\" => \"/api/stasts\"\n            \"api_version\" => null\n            \"software\" => \"pmb\"\n            \"adress\" => null\n            \"phone\" => null\n            \"fax\" => null\n            \"email\" => null\n            \"website_url\" => null\n            \"facebook_url\" => null\n            \"notes\" => \"يتم تجريب الربط\"\n            \"created_at\" => null\n            \"updated_at\" => \"2025-01-13 06:51:35\"\n            \"deleted_at\" => null\n          ]\n          #original: array:26 [\n            \"id\" => 4\n            \"name\" => \" تيندوف\"\n            \"minister\" => null\n            \"wilaya_id\" => \"37\"\n            \"municipality_id\" => null\n            \"code\" => \"3701\"\n            \"status\" => \"Open\"\n            \"agent_name\" => \"بلولة لخضر\"\n            \"ip_adress\" => \"*************\"\n            \"api_port\" => \"45380\"\n            \"api_end\" => \"/api\"\n            \"books_end_point\" => \"/api/books\"\n            \"readers_end_point\" => \"/api/reader\"\n            \"stats_end_point\" => \"/api/stasts\"\n            \"api_version\" => null\n            \"software\" => \"pmb\"\n            \"adress\" => null\n            \"phone\" => null\n            \"fax\" => null\n            \"email\" => null\n            \"website_url\" => null\n            \"facebook_url\" => null\n            \"notes\" => \"يتم تجريب الربط\"\n            \"created_at\" => null\n            \"updated_at\" => \"2025-01-13 06:51:35\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [\n            \"wilaya\" => App\\Models\\Wilaya {#2624 …36}\n          ]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [\n            0 => \"name\"\n            1 => \"code\"\n            2 => \"ip_adress\"\n            3 => \"api_port\"\n            4 => \"api_end\"\n            5 => \"books_end_point\"\n            6 => \"readers_end_point\"\n            7 => \"stats_end_point\"\n            8 => \"api_version\"\n            9 => \"status\"\n            10 => \"agent_name\"\n            11 => \"software\"\n            12 => \"wilaya_id\"\n            13 => \"municipality_id\"\n            14 => \"minister\"\n            15 => \"adress\"\n            16 => \"phone\"\n            17 => \"fax\"\n            18 => \"email\"\n            19 => \"website_url\"\n            20 => \"facebook_url\"\n            21 => \"notes\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: array:3 [\n            0 => \"created_at\"\n            1 => \"updated_at\"\n            2 => \"deleted_at\"\n          ]\n          +orderable: array:22 [\n            0 => \"id\"\n            1 => \"name\"\n            2 => \"code\"\n            3 => \"ip_adress\"\n            4 => \"api_port\"\n            5 => \"api_end\"\n            6 => \"books_end_point\"\n            7 => \"readers_end_point\"\n            8 => \"stats_end_point\"\n            9 => \"api_version\"\n            10 => \"software\"\n            11 => \"wilaya_id\"\n            12 => \"municipality_id\"\n            13 => \"minister\"\n            14 => \"adress\"\n             …7\n          ]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        4 => App\\Models\\Library {#2555\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        5 => App\\Models\\Library {#2554\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        6 => App\\Models\\Library {#2553\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        7 => App\\Models\\Library {#2552\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        8 => App\\Models\\Library {#2551\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        9 => App\\Models\\Library {#2550\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        10 => App\\Models\\Library {#2549\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        11 => App\\Models\\Library {#2548\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        12 => App\\Models\\Library {#2546\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        13 => App\\Models\\Library {#2545\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        14 => App\\Models\\Library {#2544\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        15 => App\\Models\\Library {#2543\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        16 => App\\Models\\Library {#2542\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        17 => App\\Models\\Library {#2541\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        18 => App\\Models\\Library {#2540\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        19 => App\\Models\\Library {#2539\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        20 => App\\Models\\Library {#2538\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        21 => App\\Models\\Library {#2537\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        22 => App\\Models\\Library {#2536\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        23 => App\\Models\\Library {#2565\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        24 => App\\Models\\Library {#2566\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        25 => App\\Models\\Library {#2567\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        26 => App\\Models\\Library {#2568\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        27 => App\\Models\\Library {#2569\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        28 => App\\Models\\Library {#2570\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        29 => App\\Models\\Library {#2571\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        30 => App\\Models\\Library {#2572\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        31 => App\\Models\\Library {#2573\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        32 => App\\Models\\Library {#2574\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        33 => App\\Models\\Library {#2575\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        34 => App\\Models\\Library {#2576\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        35 => App\\Models\\Library {#2577\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        36 => App\\Models\\Library {#2578\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        37 => App\\Models\\Library {#2579\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        38 => App\\Models\\Library {#2580\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        39 => App\\Models\\Library {#2581\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        40 => App\\Models\\Library {#2582\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        41 => App\\Models\\Library {#2583\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        42 => App\\Models\\Library {#2584\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        43 => App\\Models\\Library {#2585\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n        44 => App\\Models\\Library {#2586\n          #connection: \"pgsql\"\n          #table: \"libraries\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [ …26]\n          #original: array:26 [ …26]\n          #changes: []\n          #previous: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [ …1]\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:22 [ …22]\n          #guarded: array:1 [ …1]\n          #dates: array:3 [ …3]\n          +orderable: array:22 [ …22]\n          +filterable: array:22 [ …22]\n          #forceDeleting: false\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"hoveredLibrary\" => null\n    \"libraryDetails\" => array:45 [\n      1101 => array:6 [\n        \"name\" => \" تمنراست\"\n        \"code\" => \"1101\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/1\"\n      ]\n      \"0101\" => array:6 [\n        \"name\" => \" ادرار\"\n        \"code\" => \"0101\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/2\"\n      ]\n      1301 => array:6 [\n        \"name\" => \"تلمسان\"\n        \"code\" => \"1301\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/3\"\n      ]\n      3701 => array:6 [\n        \"name\" => \" تيندوف\"\n        \"code\" => \"3701\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/4\"\n      ]\n      1701 => array:6 [\n        \"name\" => \"الجلفة\"\n        \"code\" => \"1701\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/5\"\n      ]\n      2101 => array:6 [\n        \"name\" => \"سكيكدة\"\n        \"code\" => \"2101\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/6\"\n      ]\n      1201 => array:6 [\n        \"name\" => \"تبسة\"\n        \"code\" => \"1201\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/7\"\n      ]\n      \"0301\" => array:6 [\n        \"name\" => \"الاغواط\"\n        \"code\" => \"0301\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/8\"\n      ]\n      2001 => array:6 [\n        \"name\" => \"سعيدة\"\n        \"code\" => \"2001\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/9\"\n      ]\n      3301 => array:6 [\n        \"name\" => \"ايليزي\"\n        \"code\" => \"3301\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/10\"\n      ]\n      \"0401\" => array:6 [\n        \"name\" => \" ام البواقي\"\n        \"code\" => \"0401\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/11\"\n      ]\n      \"0701\" => array:6 [\n        \"name\" => \"بسكرة\"\n        \"code\" => \"0701\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/12\"\n      ]\n      1801 => array:6 [\n        \"name\" => \"جيجل\"\n        \"code\" => \"1801\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/13\"\n      ]\n      2501 => array:6 [\n        \"name\" => \"قسنطينة\"\n        \"code\" => \"2501\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/14\"\n      ]\n      3501 => array:6 [\n        \"name\" => \"بومرداس\"\n        \"code\" => \"3501\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/15\"\n      ]\n      4001 => array:6 [\n        \"name\" => \"خنشلة\"\n        \"code\" => \"4001\"\n        \"status\" => \"widgets.closed\"\n        \"wilaya\" => null\n        \"status_color\" => \"#ff6060\"\n        \"link\" => \"http://bplpnet.test/libraries/16\"\n      ]\n      4701 => array:6 [\n        \"name\" => \"غرداية\"\n        \"code\" => \"4701\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/17\"\n      ]\n      3201 => array:6 [\n        \"name\" => \"البيض\"\n        \"code\" => \"3201\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/18\"\n      ]\n      4501 => array:6 [\n        \"name\" => \"النعامة\"\n        \"code\" => \"4501\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/19\"\n      ]\n      4401 => array:6 [\n        \"name\" => \"عين الدفلة\"\n        \"code\" => \"4401\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/20\"\n      ]\n      \"0501\" => array:6 [\n        \"name\" => \"باتنة\"\n        \"code\" => \"0501\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/21\"\n      ]\n      4801 => array:6 [\n        \"name\" => \"غيليزان\"\n        \"code\" => \"4801\"\n        \"status\" => \"widgets.closed\"\n        \"wilaya\" => null\n        \"status_color\" => \"#ff6060\"\n        \"link\" => \"http://bplpnet.test/libraries/22\"\n      ]\n      1501 => array:6 [\n        \"name\" => \"تيزي وزو\"\n        \"code\" => \"1501\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/23\"\n      ]\n      4101 => array:6 [\n        \"name\" => \"سوق اهراس\"\n        \"code\" => \"4101\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/24\"\n      ]\n      4301 => array:6 [\n        \"name\" => \"ميلة\"\n        \"code\" => \"4301\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/25\"\n      ]\n      1401 => array:6 [\n        \"name\" => \"تيارت\"\n        \"code\" => \"1401\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/26\"\n      ]\n      3401 => array:6 [\n        \"name\" => \"بورج بوعريريج\"\n        \"code\" => \"3401\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/27\"\n      ]\n      3001 => array:6 [\n        \"name\" => \"ورقلة\"\n        \"code\" => \"3001\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/28\"\n      ]\n      1001 => array:6 [\n        \"name\" => \"البويرة\"\n        \"code\" => \"1001\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/29\"\n      ]\n      3901 => array:6 [\n        \"name\" => \"الوادي\"\n        \"code\" => \"3901\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/30\"\n      ]\n      3601 => array:6 [\n        \"name\" => \"الطارف\"\n        \"code\" => \"3601\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/31\"\n      ]\n      2201 => array:6 [\n        \"name\" => \"سيدي بلعباس\"\n        \"code\" => \"2201\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/32\"\n      ]\n      4201 => array:6 [\n        \"name\" => \"تيبازة\"\n        \"code\" => \"4201\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/33\"\n      ]\n      3801 => array:6 [\n        \"name\" => \"تسمسيلت\"\n        \"code\" => \"3801\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/34\"\n      ]\n      2601 => array:6 [\n        \"name\" => \"المدية\"\n        \"code\" => \"2601\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/35\"\n      ]\n      2901 => array:6 [\n        \"name\" => \"معسكر\"\n        \"code\" => \"2901\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/36\"\n      ]\n      \"0801\" => array:6 [\n        \"name\" => \"بشار\"\n        \"code\" => \"0801\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/37\"\n      ]\n      \"0601\" => array:6 [\n        \"name\" => \"بجاية\"\n        \"code\" => \"0601\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/38\"\n      ]\n      4601 => array:6 [\n        \"name\" => \"عين تموشنت\"\n        \"code\" => \"4601\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/39\"\n      ]\n      2301 => array:6 [\n        \"name\" => \"عنابة\"\n        \"code\" => \"2301\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/40\"\n      ]\n      \"0201\" => array:6 [\n        \"name\" => \"الشلف\"\n        \"code\" => \"0201\"\n        \"status\" => \"widgets.closed\"\n        \"wilaya\" => null\n        \"status_color\" => \"#ff6060\"\n        \"link\" => \"http://bplpnet.test/libraries/41\"\n      ]\n      2801 => array:6 [\n        \"name\" => \"مسيلة\"\n        \"code\" => \"2801\"\n        \"status\" => \"widgets.closed\"\n        \"wilaya\" => null\n        \"status_color\" => \"#ff6060\"\n        \"link\" => \"http://bplpnet.test/libraries/42\"\n      ]\n      1600 => array:6 [\n        \"name\" => \"المكتبة الوطنية الجزائرية\"\n        \"code\" => \"1600\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/43\"\n      ]\n      2701 => array:6 [\n        \"name\" => \"مستغانم \"\n        \"code\" => \"2701\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/44\"\n      ]\n      1601 => array:6 [\n        \"name\" => \"الجزائر\"\n        \"code\" => \"1601\"\n        \"status\" => \"widgets.open\"\n        \"wilaya\" => null\n        \"status_color\" => \"#22c55e\"\n        \"link\" => \"http://bplpnet.test/libraries/45\"\n      ]\n    ]\n    \"colorOpen\" => \"#22c55e\"\n    \"colorClosed\" => \"#ff6060\"\n    \"defaultSvgFill\" => \"#7c7c7c\"\n  ]\n  \"name\" => \"interactive-map\"\n  \"component\" => \"App\\Livewire\\InteractiveMap\"\n  \"id\" => \"FeX4gslLGrmMB8XiKcD9\"\n]"}, "count": 2}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG", "PHPDEBUGBAR_STACK_DATA": "[]", "_previous": "array:1 [\n  \"url\" => \"http://bplpnet.test/admin\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "23", "password_hash_web": "$2y$10$ndp0mY0vfh7SKC/wbKghcOK8n.7TdyC6/Z5j84PViILPym72FiJWy", "48040ef7f2542b39b9ba9a72983b0d88_filters": "array:3 [\n  \"library\" => null\n  \"startDate\" => null\n  \"endDate\" => null\n]"}, "request": {"data": {"status": "200 OK", "full_url": "http://bplpnet.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "telescope": "<a href=\"http://bplpnet.test/_debugbar/telescope/9f4ce643-b44f-4e1c-8b32-3446aa8ebf8b\" target=\"_blank\">View in Telescope</a>", "duration": "429ms", "peak_memory": "42MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1857970237 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1857970237\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1756818289 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"309 characters\">{&quot;data&quot;:[],&quot;memo&quot;:{&quot;id&quot;:&quot;UDFzzX0rtvjiwb98LKtl&quot;,&quot;name&quot;:&quot;app.filament.widgets.algeria-map-widget&quot;,&quot;path&quot;:&quot;admin&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;lazyLoaded&quot;:false,&quot;lazyIsolated&quot;:true,&quot;errors&quot;:[],&quot;locale&quot;:&quot;ar&quot;},&quot;checksum&quot;:&quot;c9422aa30ead538ecd6a9abdd799744a29d5e9c578d19e0cfffc09abf51b5aee&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"10 characters\">__lazyLoad</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"344 characters\">eyJkYXRhIjp7ImZvck1vdW50IjpbeyJmaWx0ZXJzIjpbeyJsaWJyYXJ5IjpudWxsLCJzdGFydERhdGUiOm51bGwsImVuZERhdGUiOm51bGx9LHsicyI6ImFyciJ9XX0seyJzIjoiYXJyIn1dfSwibWVtbyI6eyJpZCI6IjJaSWg1R0pFMGxkcEQ2Wk9QV043IiwibmFtZSI6Il9fbW91bnRQYXJhbXNDb250YWluZXIifSwiY2hlY2tzdW0iOiI4MGIzNjNmNDIyOTM2MDk4ODViYWRjZDIzZWFlMWE1MDk5ZmQ1NzI2NzhiNjgyZTQ4NTViOGM1ZjQ4MWUwZGU3In0=</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1756818289\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1931197022 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1555 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjhlNFJweU9qTWYyaXc4SEtWMDl3OGc9PSIsInZhbHVlIjoibnJRVzFVMC9nV09rS2xUUjNERDZJVG9TVlM3M3Q1RXU0S3hVMW9QRVRpMlQrc25qSm9UbFIwV1kzSzNqTUtaVU1xNExkV09NZHFLSjIyR1M3NTB1enJzbDI2bmRabmFON0VYSlN5OTFaMUdDUVVUSmMyYW15RmtjSmhtUmgyZVkzclRrTXd6eWpKbG5DUzFPY0JmaE1KQzRDVjUzQkp4Ukl4aUwwOXlRZXN6dllPL2dPbWllZE42QTRKbGFuczNMNVFENmlsaXVZb3lmcHNkZkRMejJqZDQzSHd3V3N2cU13MDJKbFJoZ0RkST0iLCJtYWMiOiI2NTRlYzEyZTY5ODY1MzRlMmY4YmM5MTM4MTdhN2Q3YWQwYzQzZTZiOGQ3YmFmZDg3YWFjMzJhMTgyYzBiMTc3IiwidGFnIjoiIn0%3D; filament_language_switch_locale=eyJpdiI6IldSalJ4dW1CcFhqWCtSR2pFSytLYUE9PSIsInZhbHVlIjoiQTVEaTRtaXhMVlhxNTBXZEUrOSt3UDVoQUJvOGdCa21ZODBwekJ5aDc5bjJ2SzgySVR1YnpMOVNWWlpSdjVWcyIsIm1hYyI6ImUyNjI0NmUyMzk3MjI2MmUyNzdiY2JhOGVhMzE0YjRjOWIyNzJiODUxMGU1NzQzYTJhY2M1YjIwYjc0ZTJkOTgiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6IlJRWml5VDhKZUR3ZCs4RWVjRDBDckE9PSIsInZhbHVlIjoiNjFaVkJUQ1l0aStsdUJhcUVFWitCdTVHSU13TjF4cUZGYkdhVWduZGlWb2o0Y0thbE4zcnBXRE82MlZDaXlTQVJRMXVQa2lkMTFYQXZCNEF4SHhTbUZUb2s1dFczUHQySi9wR2N2cGJvMW5zN1ZEdHlVNFZacXhwSW5oQXczL3AiLCJtYWMiOiI2ZWFlOWQ4ZDRkNTM3YjYzNzhkMzU4OGQyMzVhYzllZDI3NDU1YjdiMDM5Yzc5M2FjOTlkNmU0MGI2M2U0OGM2IiwidGFnIjoiIn0%3D; alfhrs_almohd_session=eyJpdiI6IkVBeCtjRjhhOXNLdUxjRmFkdEVOV0E9PSIsInZhbHVlIjoiY01ta2dHOEQ2MnJjN2dwcjJNaFRualVHYlIxM0RyWmRGcFhlZysvZEtmOURjd2UwSU5UT1ZPK25FUU0vbGxQbDRGNHV1N1NsQmZjRk56NlV4aG1PcGtycDlEYThyWU0wUUtLZG1lTy9uQkNmVm1ORGJ3dGorbjdwOE5weUhuWE4iLCJtYWMiOiI5OTJiY2M3ZTNmNDQxNTljYTdlYWQ2MjE0ZTZjYzM2NzlkZjA0NTlmYmIzODc1OTBmYTk3ZTMyYTZhN2E0MmEyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">ar-EG,ar;q=0.9,en-US;q=0.8,en;q=0.7,fr;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://bplpnet.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">http://bplpnet.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">848</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">bplpnet.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1931197022\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1854923520 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"124 characters\">23|oayZmX4hCkVRzDYgX8XiyDlbdUWGhjg8WRylkmFAigbARBfuImkVfTcPvqxq|$2y$10$ndp0mY0vfh7SKC/wbKghcOK8n.7TdyC6/Z5j84PViILPym72FiJWy</span>\"\n  \"<span class=sf-dump-key>filament_language_switch_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG</span>\"\n  \"<span class=sf-dump-key>alfhrs_almohd_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OHrIdfK5jqy65dnTOAgZp53RllI0Bol0hjZv4x8G</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1854923520\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-18487290 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 03 Jul 2025 07:04:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-18487290\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-770036659 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://bplpnet.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$10$ndp0mY0vfh7SKC/wbKghcOK8n.7TdyC6/Z5j84PViILPym72FiJWy</span>\"\n  \"<span class=sf-dump-key>48040ef7f2542b39b9ba9a72983b0d88_filters</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>library</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>startDate</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>endDate</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-770036659\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://bplpnet.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}