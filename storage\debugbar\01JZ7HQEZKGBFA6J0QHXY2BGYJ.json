{"__meta": {"id": "01JZ7HQEZKGBFA6J0QHXY2BGYJ", "datetime": "2025-07-03 08:06:30", "utime": **********.772347, "method": "GET", "uri": "/admin", "ip": "127.0.0.1"}, "php": {"version": "8.4.4", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.163255, "end": **********.77238, "duration": 0.6091251373291016, "duration_str": "609ms", "measures": [{"label": "Booting", "start": **********.163255, "relative_start": 0, "end": **********.4541, "relative_end": **********.4541, "duration": 0.****************, "duration_str": "291ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.454113, "relative_start": 0.*****************, "end": **********.772383, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "318ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.465292, "relative_start": 0.*****************, "end": **********.467596, "relative_end": **********.467596, "duration": 0.****************, "duration_str": "2.3ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.768438, "relative_start": 0.****************, "end": **********.768611, "relative_end": **********.768611, "duration": 0.0001728534698486328, "duration_str": "173μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.77018, "relative_start": 0.****************, "end": **********.770247, "relative_end": **********.770247, "duration": 6.699562072753906e-05, "duration_str": "67μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "route": {"uri": "GET admin", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, BezhanSalleh\\FilamentLanguageSwitch\\Http\\Middleware\\SwitchLanguageLocale, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Filament\\Http\\Middleware\\Authenticate", "uses": "App\\Filament\\Pages\\Dashboard@__invoke", "controller": "App\\Filament\\Pages\\Dashboard@render<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "filament.admin.pages.dashboard", "prefix": "admin/", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>"}, "queries": {"count": 9, "nb_statements": 8, "nb_visible_statements": 9, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.05357, "accumulated_duration_str": "53.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, {"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Support/Utils.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Support\\Utils.php", "line": 114}, {"index": 17, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasPanelShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasPanelShield.php", "line": 17}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 208}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 54}], "start": **********.485549, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "FilamentShield.php:121", "source": {"index": 15, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FFilamentShield.php&line=121", "ajax": false, "filename": "FilamentShield.php", "line": "121"}, "connection": "mcabplpnet", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from \"roles\" where (\"name\" = 'panel_user') limit 1", "type": "query", "params": [], "bindings": ["panel_user"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, {"index": 21, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Support/Utils.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Support\\Utils.php", "line": 114}, {"index": 22, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasPanelShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasPanelShield.php", "line": 17}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 208}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 54}], "start": **********.486093, "duration": 0.0338, "duration_str": "33.8ms", "memory": 0, "memory_str": null, "filename": "FilamentShield.php:121", "source": {"index": 20, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FFilamentShield.php&line=121", "ajax": false, "filename": "FilamentShield.php", "line": "121"}, "connection": "mcabplpnet", "explain": null, "start_percent": 0, "width_percent": 63.095}, {"sql": "select * from \"users\" where \"id\" = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.5207741, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "mcabplpnet", "explain": null, "start_percent": 63.095, "width_percent": 1.829}, {"sql": "select \"name\", \"id\" from \"libraries\" where \"status\" = 'Open' and \"libraries\".\"deleted_at\" is null", "type": "query", "params": [], "bindings": ["Open"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Pages\\Dashboard.php", "line": 27}, {"index": 15, "namespace": null, "name": "vendor/filament/filament/src/Pages/Dashboard/Concerns/HasFiltersForm.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Pages\\Dashboard\\Concerns\\HasFiltersForm.php", "line": 29}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Pages/Dashboard/Concerns/HasFiltersForm.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Pages\\Dashboard\\Concerns\\HasFiltersForm.php", "line": 14}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Concerns/InteractsWithForms.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\forms\\src\\Concerns\\InteractsWithForms.php", "line": 411}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Concerns/InteractsWithForms.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\forms\\src\\Concerns\\InteractsWithForms.php", "line": 365}], "start": **********.555577, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "Dashboard.php:27", "source": {"index": 14, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "C:\\laragon\\www\\BplpNet\\app\\Filament\\Pages\\Dashboard.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FFilament%2FPages%2FDashboard.php&line=27", "ajax": false, "filename": "Dashboard.php", "line": "27"}, "connection": "mcabplpnet", "explain": null, "start_percent": 64.924, "width_percent": 2.707}, {"sql": "select \"permissions\".*, \"model_has_permissions\".\"model_id\" as \"pivot_model_id\", \"model_has_permissions\".\"permission_id\" as \"pivot_permission_id\", \"model_has_permissions\".\"model_type\" as \"pivot_model_type\" from \"permissions\" inner join \"model_has_permissions\" on \"permissions\".\"id\" = \"model_has_permissions\".\"permission_id\" where \"model_has_permissions\".\"model_id\" in (23) and \"model_has_permissions\".\"model_type\" = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": **********.635613, "duration": 0.00228, "duration_str": "2.28ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "mcabplpnet", "explain": null, "start_percent": 67.631, "width_percent": 4.256}, {"sql": "select \"roles\".*, \"model_has_roles\".\"model_id\" as \"pivot_model_id\", \"model_has_roles\".\"role_id\" as \"pivot_role_id\", \"model_has_roles\".\"model_type\" as \"pivot_model_type\" from \"roles\" inner join \"model_has_roles\" on \"roles\".\"id\" = \"model_has_roles\".\"role_id\" where \"model_has_roles\".\"model_id\" in (23) and \"model_has_roles\".\"model_type\" = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 314}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}], "start": **********.639807, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "mcabplpnet", "explain": null, "start_percent": 71.887, "width_percent": 2.539}, {"sql": "select count(*) as aggregate from \"roles\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Resources/RoleResource.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Resources\\RoleResource.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.652483, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:206", "source": {"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Resources/RoleResource.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Resources\\RoleResource.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FResources%2FRoleResource.php&line=206", "ajax": false, "filename": "RoleResource.php", "line": "206"}, "connection": "mcabplpnet", "explain": null, "start_percent": 74.426, "width_percent": 1.325}, {"sql": "select * from \"media\" where \"media\".\"model_id\" in (23) and \"media\".\"model_type\" = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 279}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 311}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 298}], "start": **********.690428, "duration": 0.01242, "duration_str": "12.42ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:635", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=635", "ajax": false, "filename": "InteractsWithMedia.php", "line": "635"}, "connection": "mcabplpnet", "explain": null, "start_percent": 75.751, "width_percent": 23.185}, {"sql": "select count(*) as aggregate from \"roles\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Resources/RoleResource.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Resources\\RoleResource.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.717845, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:206", "source": {"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Resources/RoleResource.php", "file": "C:\\laragon\\www\\BplpNet\\vendor\\bezhansalleh\\filament-shield\\src\\Resources\\RoleResource.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FResources%2FRoleResource.php&line=206", "ajax": false, "filename": "RoleResource.php", "line": "206"}, "connection": "mcabplpnet", "explain": null, "start_percent": 98.936, "width_percent": 1.064}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 3, "is_counter": true}, "livewire": {"data": {"app.filament.pages.dashboard #TIAnU46LxcSQn8TeF3ap": "array:4 [\n  \"data\" => array:16 [\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"areFormStateUpdateHooksDisabledForTesting\" => false\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"filters\" => array:3 [\n      \"library\" => null\n      \"startDate\" => null\n      \"endDate\" => null\n    ]\n  ]\n  \"name\" => \"app.filament.pages.dashboard\"\n  \"component\" => \"App\\Filament\\Pages\\Dashboard\"\n  \"id\" => \"TIAnU46LxcSQn8TeF3ap\"\n]", "filament-language-switch #VZRpsIdKHXymYCQWh0rF": "array:4 [\n  \"data\" => []\n  \"name\" => \"filament-language-switch\"\n  \"component\" => \"BezhanSalleh\\FilamentLanguageSwitch\\Http\\Livewire\\FilamentLanguageSwitch\"\n  \"id\" => \"VZRpsIdKHXymYCQWh0rF\"\n]", "filament.livewire.notifications #2vkHgclPcXG3xWLRvCxr": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#3211\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"2vkHgclPcXG3xWLRvCxr\"\n]"}, "count": 3}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 52, "messages": [{"message": "[\n  ability => page_FennecBackup,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1195394181 data-indent-pad=\"  \"><span class=sf-dump-note>page_FennecBackup </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">page_FennecBackup</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1195394181\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.643266, "xdebug_link": null}, {"message": "[\n  ability => page_FennecBackup,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-769574043 data-indent-pad=\"  \"><span class=sf-dump-note>page_FennecBackup </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">page_FennecBackup</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-769574043\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.644361, "xdebug_link": null}, {"message": "[\n  ability => page_BookSearchApiPage,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-601742988 data-indent-pad=\"  \"><span class=sf-dump-note>page_BookSearchApiPage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">page_BookSearchApiPage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-601742988\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.647593, "xdebug_link": null}, {"message": "[\n  ability => page_BookSearchApiPage,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1339579356 data-indent-pad=\"  \"><span class=sf-dump-note>page_BookSearchApiPage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">page_BookSearchApiPage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1339579356\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.648016, "xdebug_link": null}, {"message": "[\n  ability => page_SystemDashboard,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-409156156 data-indent-pad=\"  \"><span class=sf-dump-note>page_SystemDashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">page_SystemDashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-409156156\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.648839, "xdebug_link": null}, {"message": "[\n  ability => page_SystemDashboard,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1483486662 data-indent-pad=\"  \"><span class=sf-dump-note>page_SystemDashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">page_SystemDashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1483486662\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.649233, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2055075442 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2055075442\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.650855, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user => 23,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1965785401 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1965785401\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.650976, "xdebug_link": null}, {"message": "[\n  ability => view_any_monitored::scheduled::task,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1161704747 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_monitored::scheduled::task </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"35 characters\">view_any_monitored::scheduled::task</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1161704747\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.65492, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTask,\n  result => true,\n  user => 23,\n  arguments => [0 => Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTask]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2050346813 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTask</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"52 characters\">Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTask</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"59 characters\">[0 =&gt; Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTask]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2050346813\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.655098, "xdebug_link": null}, {"message": "[\n  ability => view_any_monitored::scheduled::task::log::item,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-860807803 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_monitored::scheduled::task::log::item </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"46 characters\">view_any_monitored::scheduled::task::log::item</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-860807803\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.65713, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\ScheduleMonitor\\Models\\MonitoredScheduledTaskLogItem,\n  result => true,\n  user => 23,\n  arguments => [0 => <PERSON><PERSON>\\ScheduleMonitor\\Models\\MonitoredScheduledTaskLogItem]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTaskLogItem</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"59 characters\">Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTaskLogItem</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"66 characters\">[0 =&gt; Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTaskLogItem]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.657273, "xdebug_link": null}, {"message": "[\n  ability => view_any_api::health::check,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>view_any_api::health::check </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">view_any_api::health::check</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.659397, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ApiHealthCheck,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\ApiHealthCheck]\n]", "message_html": "<pre class=sf-dump id=sf-dump-899741835 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ApiHealthCheck</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\ApiHealthCheck</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; App\\Models\\ApiHealthCheck]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-899741835\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.659525, "xdebug_link": null}, {"message": "[\n  ability => view_any_document::record,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-408117290 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_document::record </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">view_any_document::record</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-408117290\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.661446, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\DocumentRecord,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\DocumentRecord]\n]", "message_html": "<pre class=sf-dump id=sf-dump-795574025 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\DocumentRecord</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\DocumentRecord</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; App\\Models\\DocumentRecord]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-795574025\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.661585, "xdebug_link": null}, {"message": "[\n  ability => view_any_library,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1003531372 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_library </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_library</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1003531372\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.662875, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Library,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\Library]\n]", "message_html": "<pre class=sf-dump id=sf-dump-535541222 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Library</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Library</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Library]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-535541222\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.663012, "xdebug_link": null}, {"message": "[\n  ability => view_any_library::stats,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-698177645 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_library::stats </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view_any_library::stats</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-698177645\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.664384, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\LibraryStats,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\LibraryStats]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1281220769 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\LibraryStats</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\LibraryStats</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\LibraryStats]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1281220769\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.66451, "xdebug_link": null}, {"message": "[\n  ability => view_any_municipality,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-170889613 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_municipality </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">view_any_municipality</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-170889613\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.6659, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Municipality,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\Municipality]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2027640216 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Municipality</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\Municipality</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\Municipality]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2027640216\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.666025, "xdebug_link": null}, {"message": "[\n  ability => view_any_user,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-738203824 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-738203824\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.667185, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1017656508 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1017656508\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.667303, "xdebug_link": null}, {"message": "[\n  ability => view_any_wilaya,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-132180747 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_wilaya </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_wilaya</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-132180747\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.668919, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Wilaya,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\Wilaya]\n]", "message_html": "<pre class=sf-dump id=sf-dump-856787193 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Wilaya</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Wilaya</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Wilaya]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-856787193\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.669041, "xdebug_link": null}, {"message": "[\n  ability => page_FennecBackup,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1452380930 data-indent-pad=\"  \"><span class=sf-dump-note>page_FennecBackup </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">page_FennecBackup</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1452380930\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.714309, "xdebug_link": null}, {"message": "[\n  ability => page_FennecBackup,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-437079049 data-indent-pad=\"  \"><span class=sf-dump-note>page_FennecBackup </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">page_FennecBackup</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-437079049\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.714706, "xdebug_link": null}, {"message": "[\n  ability => page_BookSearchApiPage,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1444066754 data-indent-pad=\"  \"><span class=sf-dump-note>page_BookSearchApiPage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">page_BookSearchApiPage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1444066754\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.715311, "xdebug_link": null}, {"message": "[\n  ability => page_BookSearchApiPage,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1049254695 data-indent-pad=\"  \"><span class=sf-dump-note>page_BookSearchApiPage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">page_BookSearchApiPage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1049254695\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.715675, "xdebug_link": null}, {"message": "[\n  ability => page_SystemDashboard,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2070897211 data-indent-pad=\"  \"><span class=sf-dump-note>page_SystemDashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">page_SystemDashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2070897211\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.716268, "xdebug_link": null}, {"message": "[\n  ability => page_SystemDashboard,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-578545625 data-indent-pad=\"  \"><span class=sf-dump-note>page_SystemDashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">page_SystemDashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-578545625\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.716626, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2022805200 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2022805200\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.717381, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user => 23,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1420678231 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1420678231\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.717493, "xdebug_link": null}, {"message": "[\n  ability => view_any_monitored::scheduled::task,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-473009023 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_monitored::scheduled::task </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"35 characters\">view_any_monitored::scheduled::task</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-473009023\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.719514, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTask,\n  result => true,\n  user => 23,\n  arguments => [0 => Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTask]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1229386862 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTask</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"52 characters\">Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTask</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"59 characters\">[0 =&gt; Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTask]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1229386862\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.719582, "xdebug_link": null}, {"message": "[\n  ability => view_any_monitored::scheduled::task::log::item,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-927638219 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_monitored::scheduled::task::log::item </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"46 characters\">view_any_monitored::scheduled::task::log::item</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-927638219\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.719994, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\ScheduleMonitor\\Models\\MonitoredScheduledTaskLogItem,\n  result => true,\n  user => 23,\n  arguments => [0 => <PERSON><PERSON>\\ScheduleMonitor\\Models\\MonitoredScheduledTaskLogItem]\n]", "message_html": "<pre class=sf-dump id=sf-dump-136053502 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTaskLogItem</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"59 characters\">Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTaskLogItem</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"66 characters\">[0 =&gt; Spatie\\ScheduleMonitor\\Models\\MonitoredScheduledTaskLogItem]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-136053502\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.720169, "xdebug_link": null}, {"message": "[\n  ability => view_any_api::health::check,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>view_any_api::health::check </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">view_any_api::health::check</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.720978, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ApiHealthCheck,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\ApiHealthCheck]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ApiHealthCheck</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\ApiHealthCheck</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; App\\Models\\ApiHealthCheck]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.721116, "xdebug_link": null}, {"message": "[\n  ability => view_any_document::record,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>view_any_document::record </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">view_any_document::record</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.721941, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\DocumentRecord,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\DocumentRecord]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1130233867 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\DocumentRecord</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\DocumentRecord</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; App\\Models\\DocumentRecord]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1130233867\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.722177, "xdebug_link": null}, {"message": "[\n  ability => view_any_library,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1908074170 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_library </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_library</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1908074170\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.722973, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Library,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\Library]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1071412306 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Library</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Library</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Library]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1071412306\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.723098, "xdebug_link": null}, {"message": "[\n  ability => view_any_library::stats,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-632196313 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_library::stats </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view_any_library::stats</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-632196313\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.723851, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\LibraryStats,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\LibraryStats]\n]", "message_html": "<pre class=sf-dump id=sf-dump-314393894 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\LibraryStats</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\LibraryStats</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\LibraryStats]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-314393894\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.723973, "xdebug_link": null}, {"message": "[\n  ability => view_any_municipality,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1543118411 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_municipality </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">view_any_municipality</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1543118411\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.724723, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Municipality,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\Municipality]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2098932513 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Municipality</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\Municipality</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\Municipality]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2098932513\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.724897, "xdebug_link": null}, {"message": "[\n  ability => view_any_user,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-928473067 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-928473067\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.725634, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-720347102 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-720347102\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.725743, "xdebug_link": null}, {"message": "[\n  ability => view_any_wilaya,\n  target => null,\n  result => true,\n  user => 23,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1596117709 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_wilaya </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_wilaya</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1596117709\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.726547, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Wilaya,\n  result => true,\n  user => 23,\n  arguments => [0 => App\\Models\\Wilaya]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1787430654 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Wilaya</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Wilaya</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Wilaya]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1787430654\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.726692, "xdebug_link": null}]}, "session": {"_token": "fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG", "PHPDEBUGBAR_STACK_DATA": "[]", "_previous": "array:1 [\n  \"url\" => \"http://bplpnet.test/admin\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "23", "password_hash_web": "$2y$10$ndp0mY0vfh7SKC/wbKghcOK8n.7TdyC6/Z5j84PViILPym72FiJWy", "48040ef7f2542b39b9ba9a72983b0d88_filters": "array:3 [\n  \"library\" => null\n  \"startDate\" => null\n  \"endDate\" => null\n]"}, "request": {"data": {"status": "200 OK", "full_url": "http://bplpnet.test/admin", "action_name": "filament.admin.pages.dashboard", "controller_action": "App\\Filament\\Pages\\Dashboard", "uri": "GET admin", "controller": "App\\Filament\\Pages\\Dashboard@render<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2FBplpNet%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:admin, panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, BezhanSalleh\\FilamentLanguageSwitch\\Http\\Middleware\\SwitchLanguageLocale, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Filament\\Http\\Middleware\\Authenticate", "telescope": "<a href=\"http://bplpnet.test/_debugbar/telescope/9f4ce6d5-6fca-417d-8ccf-03af81c537a0\" target=\"_blank\">View in Telescope</a>", "duration": "611ms", "peak_memory": "46MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1555 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjhlNFJweU9qTWYyaXc4SEtWMDl3OGc9PSIsInZhbHVlIjoibnJRVzFVMC9nV09rS2xUUjNERDZJVG9TVlM3M3Q1RXU0S3hVMW9QRVRpMlQrc25qSm9UbFIwV1kzSzNqTUtaVU1xNExkV09NZHFLSjIyR1M3NTB1enJzbDI2bmRabmFON0VYSlN5OTFaMUdDUVVUSmMyYW15RmtjSmhtUmgyZVkzclRrTXd6eWpKbG5DUzFPY0JmaE1KQzRDVjUzQkp4Ukl4aUwwOXlRZXN6dllPL2dPbWllZE42QTRKbGFuczNMNVFENmlsaXVZb3lmcHNkZkRMejJqZDQzSHd3V3N2cU13MDJKbFJoZ0RkST0iLCJtYWMiOiI2NTRlYzEyZTY5ODY1MzRlMmY4YmM5MTM4MTdhN2Q3YWQwYzQzZTZiOGQ3YmFmZDg3YWFjMzJhMTgyYzBiMTc3IiwidGFnIjoiIn0%3D; filament_language_switch_locale=eyJpdiI6IldSalJ4dW1CcFhqWCtSR2pFSytLYUE9PSIsInZhbHVlIjoiQTVEaTRtaXhMVlhxNTBXZEUrOSt3UDVoQUJvOGdCa21ZODBwekJ5aDc5bjJ2SzgySVR1YnpMOVNWWlpSdjVWcyIsIm1hYyI6ImUyNjI0NmUyMzk3MjI2MmUyNzdiY2JhOGVhMzE0YjRjOWIyNzJiODUxMGU1NzQzYTJhY2M1YjIwYjc0ZTJkOTgiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6IkVTZUpTU1ordk4wTGVEbVNjbVpCWkE9PSIsInZhbHVlIjoiTXpwZVdIQkNLbW5ueWJpK2IxR01nZlh2c09SbDV4QXk2bG56UG56N2t2QWNvSHRheWRiTzdXSzR0Zlh1cEhWdkk3bHIzeGxEQ1BCTUw1OVltQUJ3dG84ZzhxZVo1SEx2NkxYS0E4UE4rMDdmdGpGNVVaZnJCZDhGem5UYlY5MkIiLCJtYWMiOiI4Mjc5MjA3NmJkOGJiYzIwMjI4OWMzZGMxYTYzZWMwYjRhNzdlOGE1MzE4MzM0YjM3ZjExZjZiMjNlODAwYjg0IiwidGFnIjoiIn0%3D; alfhrs_almohd_session=eyJpdiI6Im12eERtL0VNZERjRGNBZ2RHK01CMmc9PSIsInZhbHVlIjoiSlBZVzhDNmdQVXF6dnRvOWVNMlFkSndOblVKVkZYS3NTT0RSSlNRT2pnSTRuOXMvODJaVjUxSE1LMUVYV0J6R1p3TVc0U1paZzRHY1JOanNhYTNhMmZ3dkxzWFcyNW9VUlNLU3lGQ1FubldSblptYnI0WmplUHk0M3E0RnNGckQiLCJtYWMiOiI5N2M2ZGRkNjYxZGY0ODdmZGJhM2E0ODgyMzY1MjFlMjUxNTZjNDI5Yzg4YzU0MWRmYTljOWEyOGY4YmQ3MGM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">ar-EG,ar;q=0.9,en-US;q=0.8,en;q=0.7,fr;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://bplpnet.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">bplpnet.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1423918854 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"124 characters\">23|oayZmX4hCkVRzDYgX8XiyDlbdUWGhjg8WRylkmFAigbARBfuImkVfTcPvqxq|$2y$10$ndp0mY0vfh7SKC/wbKghcOK8n.7TdyC6/Z5j84PViILPym72FiJWy</span>\"\n  \"<span class=sf-dump-key>filament_language_switch_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG</span>\"\n  \"<span class=sf-dump-key>alfhrs_almohd_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OHrIdfK5jqy65dnTOAgZp53RllI0Bol0hjZv4x8G</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1423918854\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-7038456 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 03 Jul 2025 07:06:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImxOd2QwNFVSRVUvNkNpd3NqNUxLdnc9PSIsInZhbHVlIjoiRWlmVlZvcjU5NWwyMjRoWXpCQ3RSNUgzS2JPaXZJYWpJK2Mwb1p3Qm4yOFRjSXB1UE1sR0ZDM1IxVWJWWHdqTXQ5YStkN1NTV295bkkwOFVaUmhVdC8rY29BandXQzJhT2tXSm1zMmRoRjJDZk1XcStHNzRHTVYvOWRVaTAyODUiLCJtYWMiOiIxOWFkZDNmNGFhYzE3MTJhOWNkNWEwZTk2MWM3NjZmNjM5MmExY2NmNTFmZjNjNTBhNzNkNGNlODY4ZmY0NGM1IiwidGFnIjoiIn0%3D; expires=Thu, 03 Jul 2025 09:06:30 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">alfhrs_almohd_session=eyJpdiI6InZBOGNIQ2pORFJkdk5pNEN5V3RsQ0E9PSIsInZhbHVlIjoidmMxcnZ0ZEF4ZEZ1T3NibmpMMDBqQks3cS9UQUQzWWJSUVhDTTJSNTh1RXgzVkRreHhiaUk0Tkx1c2tZa1J6T0E2b2t3elJLMVJ0MTlOOWx5dk1lOXJhWHlrL0lUd05VSHpqVnR5bzg3U0tKYzZZSlNiaXY3cWU0cDNyTUMzYjUiLCJtYWMiOiIxOTcxOTljYTE2NjAzNWNlODEwM2Y3ZDM4Yzc1MzE5MjJkOTk5NTc2OTRjMTNmMmRmY2I1ZTgzMDQ0MWQxZjdmIiwidGFnIjoiIn0%3D; expires=Thu, 03 Jul 2025 09:06:30 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImxOd2QwNFVSRVUvNkNpd3NqNUxLdnc9PSIsInZhbHVlIjoiRWlmVlZvcjU5NWwyMjRoWXpCQ3RSNUgzS2JPaXZJYWpJK2Mwb1p3Qm4yOFRjSXB1UE1sR0ZDM1IxVWJWWHdqTXQ5YStkN1NTV295bkkwOFVaUmhVdC8rY29BandXQzJhT2tXSm1zMmRoRjJDZk1XcStHNzRHTVYvOWRVaTAyODUiLCJtYWMiOiIxOWFkZDNmNGFhYzE3MTJhOWNkNWEwZTk2MWM3NjZmNjM5MmExY2NmNTFmZjNjNTBhNzNkNGNlODY4ZmY0NGM1IiwidGFnIjoiIn0%3D; expires=Thu, 03-Jul-2025 09:06:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">alfhrs_almohd_session=eyJpdiI6InZBOGNIQ2pORFJkdk5pNEN5V3RsQ0E9PSIsInZhbHVlIjoidmMxcnZ0ZEF4ZEZ1T3NibmpMMDBqQks3cS9UQUQzWWJSUVhDTTJSNTh1RXgzVkRreHhiaUk0Tkx1c2tZa1J6T0E2b2t3elJLMVJ0MTlOOWx5dk1lOXJhWHlrL0lUd05VSHpqVnR5bzg3U0tKYzZZSlNiaXY3cWU0cDNyTUMzYjUiLCJtYWMiOiIxOTcxOTljYTE2NjAzNWNlODEwM2Y3ZDM4Yzc1MzE5MjJkOTk5NTc2OTRjMTNmMmRmY2I1ZTgzMDQ0MWQxZjdmIiwidGFnIjoiIn0%3D; expires=Thu, 03-Jul-2025 09:06:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-7038456\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-830824973 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fgoZdfcmJYZcJHFEzPPzKIf1QJrGx4hJ06YCyivG</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://bplpnet.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$10$ndp0mY0vfh7SKC/wbKghcOK8n.7TdyC6/Z5j84PViILPym72FiJWy</span>\"\n  \"<span class=sf-dump-key>48040ef7f2542b39b9ba9a72983b0d88_filters</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>library</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>startDate</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>endDate</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-830824973\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://bplpnet.test/admin", "action_name": "filament.admin.pages.dashboard", "controller_action": "App\\Filament\\Pages\\Dashboard"}, "badge": null}}